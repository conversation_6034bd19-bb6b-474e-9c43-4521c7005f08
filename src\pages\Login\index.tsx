import { styled } from "@linaria/react";
import { Button, notification } from "antd";
import { theme } from "../../utils/theme";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useTranslation } from "react-i18next";
import { Input } from "../../components";
import { useMutation } from "react-query";
import { useDispatch } from "react-redux";
import { setAuthenticated, setProfileId } from "../../store/features/auth";
import { loginService } from "../../services/login";
import { API } from "../../utils/api";
// import { API } from "../../utils/api";
// import Cookies from "universal-cookie";

type FormData = {
  username: string;
  password: string;
};
const LoginPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const loginMutation = useMutation(loginService, {
    onSuccess: (data: any) => {
      dispatch(setProfileId(data?.profileId));
      localStorage.setItem("token", data.token);
      API.defaults.headers.common["CDO-TOKEN"] = data.token;
      dispatch(setAuthenticated(true));
    },
    onError: (response: any) => {
      notification.error({
        message: "Error Occurred",
        description:
          response?.data?.details || "Please try again after sometime",
      });
    },
  });

  const validationSchema = yup.object({
    username: yup.string().required("Required"),
    password: yup.string().required("Required"),
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (values) => {
    loginMutation.mutate(values);
    // const token = "cdo-front";
    // cookies.set("token", token, {
    //   path: "/",
    //   maxAge: 86400000,
    //   // secure: true, // Ensures the cookie is only sent over HTTPS
    //   // httpOnly: true, // Restricts access to the cookie to HTTP requests only
    //   sameSite: "strict", // Set the SameSite attribute
    // });
    // API.interceptors.request.use(
    //   async (config) => {
    //     config.headers["Authorization"] = "Bearer " + token;
    //     return config;
    //   },
    //   (error) => {
    //     return Promise.reject(error);
    //   }
    // );
    // notification.success({
    //   message: "Login Successful",
    // });
    // dispatch(setAuthenticated(true));
  };

  return (
    <Wrapper>
      <div>
        <h2>{t("Login")}</h2>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Input
            control={control}
            label="Username"
            name="username"
            error={errors.username?.message}
          />

          <Input
            control={control}
            name="password"
            label="Password"
            type="password"
            error={errors.password?.message}
          />

          <Button
            htmlType="submit"
            type="primary"
            loading={loginMutation.isLoading}
          >
            {t("Login")}
          </Button>
        </form>
      </div>
    </Wrapper>
  );
};

export default LoginPage;

const Wrapper = styled.div`
  background-color: #84a7c4;
  flex: 1;
  display: flex;

  & > div {
    background-color: ${theme.base};
    padding: 24px;
    width: fit-content;
    border: 5px solid ${theme.border};
    border-radius: 4px;
    margin: auto;
  }
  & h2 {
    color: ${theme.textLight};
    font-size: 17px;
    line-height: 32px;
    margin-bottom: 16px;
    text-align: center;
    font-weight: 500;
  }

  & form {
    min-width: 360px;
  }
  & button {
    width: 100%;
    height: 40px;
    margin-bottom: 14px;
    font-size: 14px;
    letter-spacing: 1px;
    font-weight: 500;
    margin-top: 5px;
    font-family: "Inter", sans-serif;
  }
`;
