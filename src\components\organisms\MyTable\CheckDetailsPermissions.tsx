import { useEffect } from "react";
import { usePermissions } from "../../../utils/functions/customHooks";
import { DetailCellRenderer } from "./DetailCellRenderer";

const CheckDetailsPermissions = (props) => {
  const { data, node, context, api } = props;
  const { getPermissions } = usePermissions();
  const permissions = getPermissions(data?.permissionsId || 0);

  useEffect(() => {
    if (!permissions.includes("VIEW")) {
      api?.setRowNodeExpanded(node, false, true);
      context.setNoPermissionPopup(node); // ✅ State update in useEffect
    }
  }, [permissions, node, context]);

  if (permissions.includes("VIEW")) {
    return <DetailCellRenderer data={data} />;
  }

  return <></>;
};

export { CheckDetailsPermissions };
