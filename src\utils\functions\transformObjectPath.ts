import { i18n } from "../../utils/i18n";

export const transformObjectPath = (path: string, inTrash: boolean) => {
  // Replace all | with >

  if (!path) {
    return "";
  }

  const transformedText = path
    .replace(/\{-?\d+\}/g, " > ")
    .replace(" | ", "|")
    .replace(/\|/g, "");

  // // Remove the trailing > if it exists
  // if (transformedText?.endsWith(">")) {
  //   transformedText = transformedText.slice(0, -1);
  // }

  const transformedPath = transformedText.startsWith(" > ")
    ? transformedText.slice(3)
    : transformedText;

  return inTrash ? `${i18n.t("Trash")} > ${transformedPath}` : transformedPath;
};
