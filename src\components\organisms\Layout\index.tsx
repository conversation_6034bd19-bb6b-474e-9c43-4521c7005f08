import { styled } from "@linaria/react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>fig<PERSON><PERSON><PERSON>, Space, Spin, Tour } from "antd";
import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";
import { Header } from "../Header";
import { Helmet } from "react-helmet";
import { HomeSidebar, Trashcan } from "../../molecules";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  getAllPermissions,
  getAllTemplates,
  getAppProperties,
  getHeaderNodes,
  getLocalSettingsDetails,
  getPermissionsActions,
  getSettingsDetails,
  getSettingsVariables,
  getTranslationsData,
  saveTranslationsData,
} from "../../../services";
import en from "antd/locale/en_US";
import pl from "antd/locale/pl_PL";
import {
  DEFAULT_COLORS,
  DEFAULT_LANGUAGES,
  DEFAULT_PL_LOCALES,
  GET_ACTIONS_LIST,
  GET_ALL_PERMISSIONS,
  GET_ALL_TEMPLATES_KEY,
  GET_APP_PROPERTIES,
  GET_BITFLAGS,
  GET_CHILDRENS,
  GET_CURRENT_USER,
  GET_GLOBAL_SETTINGS_KEY,
  GET_HEADER_MENUS,
  GET_LOCAL_SETTINGS_KEY,
  GET_PERMISSIONS_ACTIONS,
  GET_SEARCH_ENGINE_INFO,
  GET_SETTINGS_VARIABLE,
  GET_TRANSLATIONS_DATA,
  NO_USER_ASSOCIATED,
  TRASHCAN_PARENT_NODE_ID,
} from "../../../constants";
import {
  IAppProperties,
  IGlobalSettings,
  ILocalSettings,
  ITranslationsSettings,
} from "../../../interfaces";
import { useDispatch, useSelector } from "react-redux";
import {
  setApplicationName,
  setCustomTheme,
  setHomeLayout,
  setHomeVersion,
  setLogo,
  setSections,
  setShowDescription,
  setShowLogo,
} from "../../../store/features/localSettings";
import { RootState } from "../../../store";
import { useTranslation } from "react-i18next";
import {
  setAuthenticated,
  setGlobalPermissions,
  setProfileId,
  setRole,
  setUserInfo,
} from "../../../store/features/auth";
import {
  setDevMode,
  setEditorProperties,
  setHiddenAttributes,
  setLanguages,
} from "../../../store/features/globalSettings";
import translationsEn from "../../../locales/en/translation.json";
import translationsPl from "../../../locales/pl/translation.json";
import { LoadingOutlined, TranslationOutlined } from "@ant-design/icons";
import {
  useAxiosInterceptor,
  useFlags,
  useNotification,
  usePermissions,
} from "../../../utils/functions/customHooks";
import { addLocale, locale } from "primereact/api";
import i18next from "i18next";
import { getActionsList } from "../../../services/actions";
import { setNavigationItems } from "../../../store/features/navigation";
import { VersionChangeDialog } from "../VersionChangeDialog";
import { setTemplates } from "../../../store/features/templates";
import { useTourContent } from "../../../constants/useTourContent";
import { getSearchEngineInfo } from "../../../services/search";
import { getCurrentUser } from "../../../services/login";
import { getAllNodes, getBitFlags } from "../../../services/node";

const Layout = () => {
  const { t } = useTranslation();
  const { i18n } = useTranslation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const { getHomeTourContents } = useTourContent();
  useAxiosInterceptor();
  const { getFlags } = useFlags();

  const [importing, setImporting] = useState(false);
  const [remindMeLater, setRemindMeLater] = useState(false);
  const [tourOpen, setTourOpen] = useState(false);

  const {
    showInfoNotification,
    contextHolder,
    api,
    showSuccessNotification,
    showErrorNotification,
    showTourNotification,
    clearAllNotification,
  } = useNotification();

  const { getGlobalPermissions } = usePermissions();

  const { customTheme, application_name } = useSelector(
    (root: RootState) => root.localSettings
  );

  const { authenticated, profileId } = useSelector(
    (root: RootState) => root.auth
  );
  const devMode = useSelector((root: RootState) => root.globalSettings.devMode);
  const trashCollapsed = useSelector(
    (root: RootState) => root.trash.trashCollapsed
  );

  const selectedTheme = customTheme ? customTheme : DEFAULT_COLORS;

  useEffect(() => {
    if (!localStorage.getItem("token")) {
      dispatch(setAuthenticated(false));
    }

    const language = localStorage.getItem("i18nextLng");
    if (language.startsWith("en")) {
      i18n.changeLanguage("en");
    } else if (language.startsWith("pl")) {
      i18n.changeLanguage("pl");
    }
    addLocale("pl", DEFAULT_PL_LOCALES);
  }, []);

  useEffect(() => {
    locale(i18next.language.includes("pl") ? "pl" : "en");
  }, [i18next.language]);

  // for newsletter
  // useQuery(GET_PROFILE, getProfile, {
  //   enabled: !!authenticated,
  // });

  useQuery(GET_CURRENT_USER, getCurrentUser, {
    onSuccess: (data: any) => {
      dispatch(setProfileId(data?.id));
      dispatch(setAuthenticated(true));
      dispatch(setUserInfo(data));
      if (data?.profileId === NO_USER_ASSOCIATED) {
        dispatch(setLanguages(DEFAULT_LANGUAGES));
      }
    },
    onError: (error: any) => {
      if (error.status === 401) {
        localStorage.removeItem("token");
        queryClient.clear();
        dispatch(setAuthenticated(false));
      }
    },
    enabled: !!localStorage.getItem("token"),
  });

  useQuery(GET_BITFLAGS, getBitFlags, {
    enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
  });

  const { data: permissionActions } = useQuery(
    GET_PERMISSIONS_ACTIONS,
    getPermissionsActions,
    {
      enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
    }
  );

  useQuery(GET_ALL_PERMISSIONS, getAllPermissions, {
    enabled:
      !!authenticated &&
      !!permissionActions &&
      profileId !== NO_USER_ASSOCIATED,
    onSuccess: (data) => {
      dispatch(setRole(data.isAdmin ? "admin" : "user"));
      dispatch(setGlobalPermissions(getGlobalPermissions(data?.global)));
    },
  });

  // initial APIs
  const { isLoading: templatesLoading } = useQuery(
    GET_ALL_TEMPLATES_KEY,
    getAllTemplates,
    {
      enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
      onSuccess: (data) => {
        const templates = data?.map((item) => {
          return { ...item, flag: getFlags(item.bitFlag) };
        });
        const normalizedTemplatesData = templates.reduce((acc, item) => {
          acc[item.id] = item;
          return acc;
        }, {});
        dispatch(setTemplates(normalizedTemplatesData));
      },
    }
  );

  const handleImport = () => {
    const translationData = queryClient.getQueryData(
      GET_TRANSLATIONS_DATA
    ) as any;
    api.destroy();
    setImporting(true);

    const newTranslationsKeys = [];
    Object.keys(translationsEn).forEach((translationKey) => {
      if (
        !Object.keys(translationData?.body[0].value.translations.en).includes(
          translationKey
        )
      ) {
        newTranslationsKeys.push(translationKey);
      }
    });

    const newTranslations = {
      ...translationData?.body[0].value.translations,
    };

    translationData?.body[0].value.languages?.forEach((language) => {
      newTranslationsKeys.forEach((newLangaugeKey) => {
        newTranslations[language.value][newLangaugeKey] =
          language.value === "pl" || language?.initializeWith === "pl"
            ? translationsPl[newLangaugeKey]
            : translationsEn[newLangaugeKey];
      });
    });

    mutation.mutate({
      value: {
        ...(translationData?.body ? translationData?.body[0]?.value || {} : {}),
        translations: newTranslations,
      },
    });
  };

  const mutation = useMutation(saveTranslationsData, {
    onSuccess: () => {
      showSuccessNotification("New translations updated successfully!");
      setImporting(false);
    },
    onError: () => {
      showErrorNotification("Error in updating translations!");
      setImporting(false);
    },
  });

  // getAppProperties
  const { isLoading: isAppPropertiesLoading } = useQuery(
    GET_APP_PROPERTIES,
    getAppProperties,
    {
      onSuccess: (data: IAppProperties[]) => {
        dispatch(setAuthenticated(true));
        const devModeDetails = data?.find((prop) => prop.key === "dev.mode");
        dispatch(setDevMode(String(devModeDetails.value) === "true"));
        const menubar = data?.find(
          (prop) => prop.key === "allowed.htmltags.menubar"
        )?.value;
        const toolbar = data?.find(
          (prop) => prop.key === "allowed.htmltags.toolbar"
        )?.value;

        if (!!menubar || !!toolbar) {
          dispatch(
            setEditorProperties({
              menubar: menubar,
              toolbar: toolbar,
            })
          );
        }
      },

      retry: 1,
      // enabled: !!cookies.get("token"),
      enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
    }
  );

  // fetching actions
  useQuery(GET_ACTIONS_LIST, getActionsList, {
    enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
  });

  useQuery(
    [GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID],
    () => getAllNodes(TRASHCAN_PARENT_NODE_ID),
    {
      enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
    }
  );

  // fetching settings variables
  useQuery(GET_SETTINGS_VARIABLE, getSettingsVariables, {
    enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
  });

  useQuery(GET_SEARCH_ENGINE_INFO, getSearchEngineInfo, {
    enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
  });

  useQuery<any>(GET_GLOBAL_SETTINGS_KEY, getSettingsDetails, {
    onSuccess: (data: IGlobalSettings) => {
      if (data.body.length !== 0 && !!data.body[0]?.value?.general?.showLogo) {
        dispatch(setShowLogo(data.body[0].value.general.showLogo));
      } else {
        dispatch(setShowLogo(false));
      }

      if (data.body.length !== 0 && !!data.body[0]?.value?.general?.logo) {
        dispatch(setLogo(data.body[0].value.general?.logo));
      } else {
        dispatch(setLogo(""));
      }

      if (
        data.body.length !== 0 &&
        !!data.body[0]?.value?.general?.showDescription
      ) {
        dispatch(
          setShowDescription(data.body[0].value.general.showDescription)
        );
      } else {
        dispatch(setShowDescription(false));
      }
      if (
        data.body.length !== 0 &&
        !!data.body[0]?.value?.general?.application_name
      ) {
        dispatch(
          setApplicationName(data.body[0]?.value?.general?.application_name)
        );
      }

      if (
        data.body.length !== 0 &&
        !!data.body[0]?.value?.general?.hiddenAttributes
      ) {
        dispatch(
          setHiddenAttributes(
            data.body[0].value.general.hiddenAttributes.split(",")
          )
        );
      }
    },
    enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
  });

  const { isLoading: isHeaderMenuLoading } = useQuery<any>(
    GET_HEADER_MENUS,
    getHeaderNodes,
    {
      enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
    }
  );

  function filterObject(obj1, obj2) {
    for (const key in obj1) {
      if (!(key in obj2)) {
        delete obj1[key];
      }
    }
    return obj1;
  }

  useQuery<any>(GET_TRANSLATIONS_DATA, getTranslationsData, {
    onSuccess: (data: ITranslationsSettings) => {
      if (data.body.length !== 0 && !!data.body[0].value.translations) {
        if (
          Object.keys(translationsEn).length >
            Object.keys(data.body[0].value.translations?.en || {}).length &&
          !remindMeLater
        ) {
          showInfoNotification(
            "Do you want to upload them now?",
            "New translations are available!",
            <Space>
              <Button
                onClick={() => {
                  setRemindMeLater(true);
                  api.destroy();
                }}
              >
                {t("Remind me later")}
              </Button>
              <Button type="primary" onClick={handleImport}>
                {t("Import now")}
              </Button>
            </Space>,
            <TranslationOutlined style={{ color: "#084375" }} />
          );
        }
        Object.keys(data.body[0].value.translations).forEach((lang) => {
          if (lang === "pl" || lang === "en") {
            const filteredTranslations = filterObject(
              { ...data.body[0].value.translations[lang] },
              translationsPl
            );
            i18n.addResourceBundle(
              lang,
              "translation",
              filteredTranslations,
              true,
              true
            );
          } else {
            i18n.addResourceBundle(
              lang,
              "translation",
              data.body[0].value.translations[lang],
              true,
              true
            );
          }
        });
      }

      if (data.body.length !== 0 && !!data.body[0].value.languages) {
        dispatch(setLanguages(data.body[0].value.languages));
      }
    },
    enabled: !!authenticated,
  });

  const { isLoading: isLocalSettingLoading } = useQuery(
    GET_LOCAL_SETTINGS_KEY,
    getLocalSettingsDetails,
    {
      onSuccess: (data: ILocalSettings) => {
        // if (data && data?.body[0]?.value?.language) {
        //   i18n.changeLanguage(data?.body[0]?.value?.language || "en");
        // }

        if (data && data?.body[0]?.value?.colors) {
          document.body.style.setProperty(
            "--color-trash",
            data?.body[0]?.value?.colors?.trashBreadcrumbsColor
          );
          document.body.style.setProperty(
            "--color-text",
            data?.body[0]?.value?.colors?.colorPrimary
          );
          document.body.style.setProperty(
            "--color-light",
            data?.body[0]?.value?.colors?.bgAttributes
          );
          dispatch(setCustomTheme(data?.body[0]?.value?.colors));
        } else {
          document.body.style.setProperty(
            "--color-trash",
            DEFAULT_COLORS.trashBreadcrumbsColor
          );
          document.body.style.setProperty(
            "--color-text",
            DEFAULT_COLORS.colorPrimary
          );
          document.body.style.setProperty(
            "--color-light",
            DEFAULT_COLORS.bgAttributes
          );
          dispatch(setCustomTheme(DEFAULT_COLORS));
        }

        if (data && data?.body[0]?.value?.navigationItems) {
          dispatch(setNavigationItems(data?.body[0]?.value?.navigationItems));
        }

        if (data && data?.body[0]?.value?.homeLayout) {
          dispatch(setHomeVersion(data?.body[0]?.value?.homeLayout));
        } else {
          dispatch(setHomeVersion("v2"));
        }

        if (data && data?.body[0]?.value?.homeSections) {
          dispatch(setSections(data.body[0].value.homeSections));
        } else {
          dispatch(setSections(["messages", "history"]));
        }

        if (data && data?.body[0]?.value?.homeLayoutType) {
          dispatch(setHomeLayout(data.body[0].value.homeLayoutType));
        }
      },
      enabled: !!authenticated && profileId !== NO_USER_ASSOCIATED,
    }
  );

  // hiding console errors in production
  if (import.meta.env.VITE_APP_BASE_URL.startsWith("/cdo-tools")) {
    // eslint-disable-next-line no-console, @typescript-eslint/no-empty-function
    console.error = () => {};
    // eslint-disable-next-line no-console, @typescript-eslint/no-empty-function
    console.warn = () => {};
  }

  useEffect(() => {
    if (!localStorage.getItem("hide-home-tour") && authenticated) {
      clearAllNotification();
      showTourNotification(setTourOpen, () => {
        localStorage.setItem("hide-home-tour", "1");
      });
    }
  }, [authenticated]);

  useEffect(() => {
    // eslint-disable-next-line no-console
    const originalConsoleError = console.error;

    // eslint-disable-next-line no-console
    console.error = (...args) => {
      const errorMessage = args[0]?.toString() || "";
      if (errorMessage.startsWith("*")) {
        return;
      }
      originalConsoleError(...args);
    };

    return () => {
      // eslint-disable-next-line no-console
      console.error = originalConsoleError; // Cleanup
    };
  }, []);

  return (
    <ConfigProvider
      theme={{ token: { ...selectedTheme } }}
      locale={i18next.language.startsWith("pl") ? pl : en}
    >
      <Helmet>
        <title>{application_name}</title>
      </Helmet>

      <Wrapper theme={selectedTheme}>
        {devMode && (
          <Alert
            closable
            type="warning"
            message={t("Devmode: ON")}
            className="dev-mode-alert"
          />
        )}

        {contextHolder}

        {isAppPropertiesLoading ||
        isLocalSettingLoading ||
        isHeaderMenuLoading ||
        templatesLoading ? (
          <div className="main-loader">
            <LoadingOutlined />
          </div>
        ) : (
          <>
            <Header />

            <Spin spinning={importing} fullscreen size="large" />
            {authenticated ? (
              <div className="sidebar-wrapper">
                <HomeSidebar />
                {!trashCollapsed && <Trashcan />}
                <Outlet />
              </div>
            ) : (
              <Outlet />
            )}
          </>
        )}
      </Wrapper>

      {tourOpen && (
        <Tour
          disabledInteraction
          open={tourOpen}
          onClose={() => setTourOpen(false)}
          steps={getHomeTourContents()}
        />
      )}
      <VersionChangeDialog />
    </ConfigProvider>
  );
};

export { Layout };

const Wrapper = styled.div<{ theme: any }>`
  & .ant-layout-sider-children {
    height: 100%;
  }

  & .main-loader {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 40px;
  }

  /* & .loader {
    position: absolute;
    z-index: 10000;
    right: 14px;
    font-size: 20px;
    top: 56px;
    color: #fff;
  } */

  & .sidebar-wrapper {
    display: flex;
    position: relative;
    flex: 1;
    overflow: hidden;

    & > div {
      background-color: #f3f3f3;
      flex: 1;
    }
  }
  & .ant-spin-fullscreen {
    z-index: 10000;
  }
  display: contents;
  & .version-tab .ant-tabs-tab-active span,
  .version-tab .ant-tabs-tab:hover span {
    color: ${({ theme }) => theme?.colorSecondary} !important;
  }
  & .version-tab .ant-tabs-tab-active {
    background: ${({ theme }) => theme?.bgLight} !important;
  }
  & thead tr {
    background: ${({ theme }) => theme?.bgAttributes} !important;
  }
  & th {
    color: gray !important;
    font-weight: 500;
    background: ${({ theme }) => theme?.bgAttributes} !important;
  }
  & .ant-float-btn-icon {
    color: ${({ theme }) => theme?.colorPrimary} !important;
  }
`;
