import { LinkOutlined, PlusOutlined } from "@ant-design/icons";
import { styled } from "@linaria/react";
import { But<PERSON>, Empty, Popconfirm, Tooltip } from "antd";
import { useEffect, useState } from "react";
import { useTheme } from "../../../../utils/useTheme";
import { MyTable } from "../../../organisms";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  useNotification,
  usePermissions,
} from "../../../../utils/functions/customHooks";
import { useDispatch } from "react-redux";
import { setBottomDrawerMask } from "../../../../store/features";
import {
  deleteAttachment,
  getAttachmentContent,
  getAttachmentsByNodeId,
  saveLocalSettings,
} from "../../../../services";
import {
  GET_COUNTERS,
  GET_LOCAL_SETTINGS_KEY,
  GET_NODE_ATTACHMENTS,
  GET_NODE_ATTRIBUTES_DETAILS,
} from "../../../../constants";
import { ILocalSettings, INodeDetails } from "../../../../interfaces";
import { setTrashcanDrawerMask } from "../../../../store/features/trashcan";
import { AddAttachment } from "./AddAttachment";
import { useTranslation } from "react-i18next";

// AttachmentContainer for displaying the list of attachments in bottom drawer
const AttachmentContainer = ({ displaySaveCancel, fromTrashcan, id }) => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { getPermissions } = usePermissions();
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const [permissions, setPermissions] = useState([]);
  const [addAttachment, setAddAttachment] = useState(false);

  const COLUMNS = [
    {
      headerName: "Attachment",
      field: "name",
      minWidth: 200,
      flex: 1,
      cellRenderer: ({ data }) => (
        <div className="attachment">
          <LinkOutlined />
          {data.originalFileName}
        </div>
      ),
    },

    {
      headerName: "Uploaded By",
      field: "user",
      width: 200,
      cellRenderer: () => "-",
    },

    {
      headerName: "Date",
      width: 150,
      field: "date",
      isDate: true,
      cellRenderer: () => {
        return "-";
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      isAction: true,
      width: 100,
      cellRenderer: ({ data }) => {
        return (
          <div className="ag-actions">
            {/* download */}
            <Tooltip title={t("Download")}>
              <div
                onClick={async () => {
                  await handleDownload(data?.id);
                  showSuccessNotification(
                    "Attachment downloaded successfully!"
                  );
                }}
              >
                <i className="pi pi-download" />
              </div>
            </Tooltip>

            {/* delete attachment */}
            <Popconfirm
              title={`${t("Delete")}?`}
              description={t("Are you sure to delete this attachment?")}
              onConfirm={async () => {
                deleteMutation.mutate(data?.id);
              }}
              okButtonProps={{ loading: deleteMutation.isLoading }}
              okText={t("Yes")}
              cancelText={t("No")}
            >
              <Tooltip title={t("Delete")}>
                <div className="ag-danger">
                  <i className="pi pi-trash" />
                </div>
              </Tooltip>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const [columns, setColumns] = useState(COLUMNS);

  const detectChange = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(true));
    } else {
      dispatch(setBottomDrawerMask(true));
    }
  };

  useEffect(() => {
    // getting permissions id from node details API cache
    const bodyData = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      id?.toString(),
    ]) as INodeDetails;
    if (bodyData) {
      setPermissions(getPermissions(bodyData?.permissionsId));
    }
  }, [id]);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.attachmentDrawer &&
      localSettingsData?.body[0]?.value?.attachmentDrawer?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.attachmentDrawer?.pinned || [];
      const sort =
        localSettingsData?.body[0]?.value?.attachmentDrawer?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.attachmentDrawer.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );

      setColumns(allColumns);
    } else {
      setColumns(COLUMNS);
    }
  }, [localSettingsData]);

  const { data, refetch, isError } = useQuery(
    [GET_NODE_ATTACHMENTS, id],
    () => getAttachmentsByNodeId(id),
    {
      enabled: !!id,
    }
  );

  const handleCancel = () => {
    setTimeout(() => {
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
    }, 200);
  };

  // download attachment
  const handleDownload = async (attachmentId) => {
    const content = await getAttachmentContent(attachmentId);
    const link = document.createElement("a");
    link.href = `data:image/png;base64, ${content?.content}`;
    link.download = content?.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setBottomDrawerMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
    },
  });

  // save table details to local settings
  const handleSave = (newColumns: string[], filters, sort, pinned) => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        attachmentDrawer: {
          columns: newColumns,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  // downloading multiple
  const handleMultipleDownload = (selected: any[]) => {
    selected?.forEach((attachment) => {
      handleDownload(attachment?.id);
    });
  };

  // deleting attachment
  const deleteMutation = useMutation(deleteAttachment, {
    onSuccess: () => {
      queryClient.invalidateQueries([GET_COUNTERS, id]);
      showSuccessNotification("Attachment deleted successfully!");
      refetch();
    },
    onError: () => {
      showErrorNotification("Error in deleting attachment!");
    },
  });

  return (
    <Wrapper
      theme={theme}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {contextHolder}

      {data?.length === 0 ? (
        <div className="empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t("No attachments")}
          />

          {permissions.includes("ATTACH") && (
            <Button onClick={() => setAddAttachment(true)} type="primary">
              {t("Add attachment")}
            </Button>
          )}
        </div>
      ) : (
        <MyTable
          fullHeight
          excelFileName="attachments"
          data={data}
          isError={isError}
          emptyMessage="No attachments"
          columns={columns}
          onDownload={(selected) => {
            handleMultipleDownload(selected);
          }}
          detectChange={detectChange}
          displaySaveCancel={displaySaveCancel}
          onCancelClick={handleCancel}
          initialFilters={
            localSettingsData?.body[0]?.value?.attachmentDrawer?.filters || {}
          }
          saveLoading={mutation.isLoading}
          onSaveClick={handleSave}
          extra={
            permissions.includes("ATTACH") && (
              <Button
                onClick={() => setAddAttachment(true)}
                type="primary"
                className="add-attachment"
              >
                <PlusOutlined />
                {t("Add Attachment")}
              </Button>
            )
          }
        />
      )}

      <AddAttachment
        visible={addAttachment}
        id={id}
        onClose={() => setAddAttachment(false)}
        afterUpload={() => {
          queryClient.invalidateQueries([GET_COUNTERS, id]);
          showSuccessNotification("Attachment uploaded successfully!");
          refetch();
        }}
      />
    </Wrapper>
  );
};

export { AttachmentContainer };

const Wrapper = styled.div<{ theme: any }>`
  height: 100%;
  overflow: auto;
  width: 100%;
  padding: 10px;

  & .add-attachment {
    box-shadow: none;
    padding: 19px 10px;
    font-size: 13px;
    border-radius: 16px;
  }
  & .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;

    & .ant-empty {
      margin-bottom: 16px;
    }

    & button {
      border: 1px solid;
      color: #084375;
      background: #fff;
      box-shadow: none;
      font-size: 13px;
    }
  }
  & .ant-float-btn-icon .anticon {
    color: #fff;
  }

  & .download,
  .delete {
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid;
  }

  & .download {
    color: var(--color-text);
  }
  & .delete {
    color: #ac0707;
  }
  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & .ant-float-btn {
    margin-bottom: 12px;
    right: 10px;
  }
  & .ant-avatar {
    border-radius: 10px;
  }

  & .attachment {
    text-align: left;
    color: ${({ theme }) => theme.colorPrimary};
    & span {
      font-size: 15px;
      margin-right: 7px;
    }
  }
`;
