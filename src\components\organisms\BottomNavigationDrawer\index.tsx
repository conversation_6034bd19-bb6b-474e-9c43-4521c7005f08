import {
  ExpandAltOutlined,
  FullscreenOutlined,
  MinusOutlined,
} from "@ant-design/icons";
import { styled } from "@linaria/react";
import { useGenerateBottomNavigationMenu } from "../../../constants/menus/bottomNavigationMenu";
import { useTheme } from "../../../utils/useTheme";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { MyTooltip } from "../../atoms";
import { DraggableTabs, ResizableDiv } from "../../molecules";
import { useLocation, useSearchParams } from "react-router-dom";
import {
  GET_COUNTERS,
  GET_NODE_ATTRIBUTES_DETAILS,
  GET_SETTINGS_VARIABLE,
  OBJECT_TEMPLATE_ID,
} from "../../../constants";
import { useQuery, useQueryClient } from "react-query";
import { getCounters } from "../../../services/counters";
import i18next from "i18next";
import { getNodeDetails } from "../../../services/node";
import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../store/features/navigation";
import { ISettingsVariable, ITemplates } from "../../../interfaces";
import { useFlags } from "../../../utils/functions/customHooks";

interface Props {
  id?: any;
  noGraph?: boolean;
  trashcan?: boolean;
  metamodel?: boolean;
  fromModal?: boolean;
  displaySaveCancel: boolean;
  fromMenuCreator?: boolean;
}

const STATE_KEY = "bottom-drawer-height";

const BottomNavigationDrawer = memo(
  ({
    noGraph,
    id,
    trashcan,
    fromMenuCreator,
    metamodel,
    fromModal,
    displaySaveCancel,
  }: Props) => {
    const theme = useTheme() as any;
    const [searchParams] = useSearchParams();
    const dispatch = useDispatch();
    const resizableRef = useRef(null);
    const queryClient = useQueryClient();
    const location = useLocation();

    const [fullScreenClicked, setFullScreenClicked] = useState(false);
    const [hideIcon, setHideIcon] = useState(false);
    const [height, setHeight] = useState(300);

    const { getFlags } = useFlags();
    const { getBottomNavigationMenus } = useGenerateBottomNavigationMenu();

    const authenticated = useSelector(
      (state: RootState) => state.auth.authenticated
    );
    const { mask, attributeMask } = useSelector(
      (state: RootState) => state.sidebar
    );
    const templatesData = useSelector(
      (state: RootState) => state.templatesStore.templates
    );
    const { trashcanDrawerMask } = useSelector(
      (state: RootState) => state.trash
    );

    const [localOpenState, setLocalOpenState] = useState(false);
    const [localSelectedState, setLocalSelectedState] = useState("");

    const { navigationItems, selectedBottomNavbar, bottomNavbarOpen } =
      useSelector((state: RootState) => state.navigation);

    const ID = id || searchParams.get("nodeId");

    const [showWorkingVersion, setShowWorkingVersion] = useState(false);

    const { data: bodyData } = useQuery(
      [GET_NODE_ATTRIBUTES_DETAILS, ID],
      () => getNodeDetails(ID),
      {
        enabled:
          !!ID &&
          !!metamodel &&
          !searchParams.get("draft") &&
          !!searchParams.get("nodeId") &&
          !!templatesData,
        cacheTime: Infinity,
      }
    );

    useEffect(() => {
      setLocalSelectedState("");
      setLocalOpenState(false);
    }, []);

    useEffect(() => {
      if (metamodel && bodyData?.bitFlag) {
        const flags = getFlags(bodyData?.bitFlag) as string[];
        setShowWorkingVersion(
          flags?.includes("EDITMODE") &&
            bodyData?.templateId === OBJECT_TEMPLATE_ID
        );
      } else {
        setShowWorkingVersion(false);
      }
    }, [bodyData]);

    const settingsVariables = queryClient.getQueryData(
      GET_SETTINGS_VARIABLE
    ) as ISettingsVariable;

    const { data: countersData } = useQuery(
      [GET_COUNTERS, ID],
      () => getCounters(ID),
      {
        enabled: !searchParams.get("draft") && !!ID && !!authenticated,
      }
    );

    const selectedNodeTemplate = useMemo(() => {
      return (
        templatesData && (templatesData[bodyData?.templateId] as ITemplates)
      );
    }, [templatesData, bodyData?.templateId]);

    const IS_MODEL = location.pathname.includes("details");

    const [items, setItems] = useState([
      ...getBottomNavigationMenus(
        noGraph,
        ID,
        countersData,
        showWorkingVersion,
        bodyData?.templateId === OBJECT_TEMPLATE_ID,
        trashcan,
        displaySaveCancel,
        fromModal ? localOpenState : bottomNavbarOpen,
        settingsVariables?.CONNECTOR_EXTRACTOR_TEMPLATE_IDS?.includes(
          bodyData?.templateId
        ),
        templatesData ? selectedNodeTemplate?.testDqm : null, //for dqm tabs
        selectedNodeTemplate?.actions?.includes(4), //for logs
        IS_MODEL || fromMenuCreator,
        fromMenuCreator //from menu creator
      ),
    ]);

    useEffect(() => {
      setItems([
        ...getBottomNavigationMenus(
          noGraph,
          id || searchParams.get("nodeId"),
          countersData,
          showWorkingVersion,
          bodyData?.templateId === OBJECT_TEMPLATE_ID,
          trashcan,
          displaySaveCancel,
          fromModal ? localOpenState : bottomNavbarOpen,
          settingsVariables?.CONNECTOR_EXTRACTOR_TEMPLATE_IDS?.includes(
            bodyData?.templateId
          ),
          templatesData ? selectedNodeTemplate?.testDqm : null,
          selectedNodeTemplate?.actions?.includes(4),
          IS_MODEL || fromMenuCreator,
          fromMenuCreator
        ),
      ]);
    }, [
      countersData,
      navigationItems,
      i18next.language,
      showWorkingVersion,
      displaySaveCancel,
      bottomNavbarOpen,
      localOpenState,
      selectedNodeTemplate,
      fromMenuCreator,
    ]);

    const handleTabChange = (value) => {
      if (fromModal) {
        setLocalSelectedState(value);
        setLocalOpenState(true);
      } else {
        dispatch(setSelectedBottomNavbar(value));
        dispatch(setBottomNavbarOpen(true));
      }
    };

    const handleResize = (_event, _direction, refToElement) => {
      const parentHeight =
        refToElement.parentElement.parentElement.clientHeight;
      const resizableHeight = refToElement.clientHeight;
      setHideIcon(resizableHeight >= parentHeight);
      if (resizableHeight <= parentHeight) setHeight(refToElement.clientHeight);
    };

    const handleBrowserZoom = () => {
      if (resizableRef.current) {
        const parentHeight = resizableRef.current.parentElement.clientHeight;
        const resizableHeight = resizableRef.current.clientHeight;

        if (resizableHeight > parentHeight) {
          resizableRef.current.style.height = `${parentHeight}px`;
          setHeight(parentHeight);
          setHideIcon(true);
        } else {
          setHeight(resizableHeight);
          setHideIcon(false);
        }
      }
    };

    useEffect(() => {
      window.addEventListener("resize", handleBrowserZoom);

      return () => {
        window.removeEventListener("resize", handleBrowserZoom);
      };
    }, []);

    useEffect(() => {
      const height = localStorage.getItem(STATE_KEY);
      if (height) setHeight(Number(height));
    }, []);

    const handleExpandClick = () => {
      const parentHeight = document.getElementById(
        "details-parent-container"
      ).clientHeight;
      setHeight(parentHeight);
      setFullScreenClicked(!fullScreenClicked);
    };

    if (fromModal ? localOpenState : bottomNavbarOpen) {
      return (
        <div ref={resizableRef} className="drawer">
          <ResizableDiv
            height={height}
            defaultHeight="300px"
            resize="top"
            onResize={handleResize}
            className={`bottom-navbar ${
              trashcanDrawerMask ? "trash-bottom-drawer-mask" : ""
            }`}
            saveHeightToLocalStorage
            stateKey={STATE_KEY}
            minHeight={30}
          >
            <Wrapper
              theme={theme}
              trashcan={trashcan}
              metamodel={location.pathname?.includes("/metamodel")}
              style={{ height: "100%" }}
            >
              {(mask || attributeMask) && !fromMenuCreator && (
                <Mask className="mask" />
              )}
              <DraggableTabs
                items={items}
                onTabClick={handleTabChange}
                setItems={setItems}
                fromModal={fromModal}
                localActiveKey={localSelectedState}
                tabBarExtraContent={
                  <>
                    <div className="actions">
                      {!hideIcon && (
                        // (fullScreenClicked ? (
                        //   <>
                        //     <Tooltip target=".full-screen-exit" />

                        //     <FullscreenExitOutlined
                        //       className="full-screen-exit"
                        //       data-pr-position="left"
                        //       data-pr-tooltip={t("Exit full screen")}
                        //       data-pr-showdelay="500"
                        //       onClick={() => {
                        //         setHeight(previousHeight);
                        //         setFullScreenClicked(!fullScreenClicked);
                        //       }}
                        //     />
                        //   </>
                        // ) : (
                        <MyTooltip title="Full screen">
                          <FullscreenOutlined
                            onClick={() => {
                              handleExpandClick();
                              setHideIcon(true);
                            }}
                          />
                        </MyTooltip>
                      )}
                      <MyTooltip title="Collapse">
                        <MinusOutlined
                          onClick={() => {
                            if (fromModal) {
                              setLocalOpenState(false);
                              setLocalSelectedState("");
                            } else {
                              dispatch(setBottomNavbarOpen(false));
                              dispatch(setSelectedBottomNavbar(""));
                            }
                          }}
                        />
                      </MyTooltip>
                    </div>
                  </>
                }
              />
            </Wrapper>
          </ResizableDiv>
        </div>
      );
    }

    return (
      <Wrapper metamodel={metamodel} theme={theme} trashcan={trashcan}>
        {(mask || attributeMask) && !fromMenuCreator && (
          <Mask className="mask" />
        )}
        <DraggableTabs
          items={items}
          onTabClick={handleTabChange}
          setItems={setItems}
          fromModal={fromModal}
          localActiveKey={localSelectedState}
          tabBarExtraContent={
            <div className="actions">
              <MyTooltip title="Expand">
                <ExpandAltOutlined
                  onClick={() => {
                    if (fromModal) {
                      setLocalOpenState(true);
                      if (!localSelectedState) {
                        setLocalSelectedState("history");
                      }
                    } else {
                      dispatch(setBottomNavbarOpen(true));
                      if (!selectedBottomNavbar) {
                        dispatch(setSelectedBottomNavbar("history"));
                      }
                    }
                  }}
                />
              </MyTooltip>
            </div>
          }
        />
      </Wrapper>
    );
  }
);

export { BottomNavigationDrawer };

const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 1;
`;

const Wrapper = styled.div<{
  theme: any;
  trashcan: boolean;
  metamodel: boolean;
}>`
  border-top: ${({ theme, trashcan, metamodel }) =>
    `3px solid ${
      trashcan
        ? theme.trashBreadcrumbsColor
        : metamodel
        ? theme.metamodelBreadcrumbsColor
        : theme.colorSecondary
    }`};

  & .mask-tabs {
    height: 45px;
    width: 100%;
  }
  & .actions {
    display: flex;
    gap: 15px;
    font-size: 17px;
    color: ${({ theme }) => theme.colorPrimary};

    & button {
      padding: 0px 10px;
      font-size: 13px;
      height: 24px;
      border-radius: 3px;
    }
  }

  & .ant-tabs-nav {
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 0px;
  }
  & .ant-tabs,
  .ant-tabs-content,
  .ant-tabs-tabpane {
    height: 100%;
  }
  & .ant-tabs-content-holder {
    flex: 1;
    overflow: hidden;
  }

  & .ant-tabs-extra-content > .anticon {
    font-size: 19px;
    color: ${({ theme }) => theme.colorPrimary};
    cursor: pointer;
  }
  & .ant-tabs-tab {
    & p {
      color: ${({ theme }) => theme.colorPrimary};
      font-size: 13px;
      cursor: pointer;

      & .counter {
        background-color: ${({ theme }) => theme.bgAttributes};
        color: ${({ theme }) => theme.colorPrimary};
        min-width: 23px;
        border-radius: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        max-height: 15px;
      }

      & .suspect-counter {
        background: #f4020029;
        color: #f70000;
      }
    }
  }
`;
