import { styled } from "@linaria/react";
import { useTheme } from "../../../../utils";
import { <PERSON><PERSON>, <PERSON><PERSON>, notification } from "antd";
import { useSearchParams } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  GET_ALL_TEMPLATES_KEY,
  GET_CHILDRENS,
  GET_COUNTERS,
  GET_NODE_ATTRIBUTES_DETAILS,
  GET_WORKING_VERSION,
  METAATTRIBUTE_ID_DEFAULT_VALUES,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
} from "../../../../constants";
import { useEffect, useState } from "react";
import {
  useNotification,
  useWorkingVersionActions,
} from "../../../../utils/functions/customHooks";
import { WorkingVersionTree } from "../../WorkingVersionTree";
import { useTranslation } from "react-i18next";
import { ResizableDiv } from "../../ResizableDiv";
import { css } from "@linaria/core";
import { useFunctions } from "../../../organisms/DetailsPage/useFunctions";
import { WorkingVersionArea } from "./WorkingVersionArea";
import { RootState } from "../../../../store";
import { useDispatch, useSelector } from "react-redux";
import { setWorkingVersionMask } from "../../../../store/features";
import {
  DELETED_FLAG,
  IAttributes,
  INodeDetails,
} from "../../../../interfaces";
import { getWorkingVersion, publishWorkingVersion } from "../../../../services";
import { addAttributes, addNodeService } from "../../../../services/node";
import {
  AddNodeModal,
  DeleteAttributeTemplateModal,
  DeleteWorkingVersion,
} from "../../Modals";
import { useSidebarActions } from "../../../../utils/functions/customHooks/useSidebarActions";
import { searchRecursivelyByID } from "../../../../utils/functions/sidebarActions";
import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../../store/features/navigation";
import { setPublishWorkingVersion } from "../../../../store/features/workingVersion";

const STATE_KEY = "WORKING_VERSION_TREE";

// draft version to display for templates that has draft version
const WorkingVersion = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();

  const objectTemplateId = searchParams.get("nodeId");

  const { generateWorkingVersionData } = useWorkingVersionActions();
  const { updateAllowedChildrens } = useFunctions();
  const { showErrorNotification, contextHolder } = useNotification();
  const { updateChildNode, updateNewIdRecursively } = useSidebarActions();

  const [templateId, setTemplateId] = useState(null);
  const [isDraft, setIsDraft] = useState(false);
  const [action, setAction] = useState({
    id: "",
    key: null,
    label: "",
  } as any);
  const [treeData, setTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selected, setSelected] = useState({ keys: [], info: [] });
  const [treeWidth, setTreeWidth] = useState(260);
  const [editingAttribute, setEditingAttribute] = useState(null);
  const [attributes, setAttributes] = useState([]);
  const [mandatoryAttributes, setMandatoryAttributes] = useState([]);
  const [disabled, setDisabled] = useState(false);
  const [specialAttribute, setSpecialAttribute] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );

  const globalPermissions = useSelector(
    (state: RootState) => state.auth.globalPermissions
  );

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const { trashCollapsed, selectedTrash } = useSelector(
    (state: RootState) => state.trash
  );

  // fetch working version
  const { data: workingVersionData } = useQuery<any>(
    [GET_WORKING_VERSION, templateId],
    () => getWorkingVersion(templateId),
    {
      enabled: !!templateId && !searchParams.get("draft"),
      onSuccess: (data) => {
        setExpandedKeys([data.id]);
        const treeData = generateWorkingVersionData(data);
        setTreeData(treeData);

        setSelected({
          keys: [data.id],
          info: [
            {
              id: data.id,
              parentId: data.parentId,
              name: data.name,
              isLeaf: data.last,
            },
          ],
        });
      },
    }
  );

  const parentNodeDetails = queryClient.getQueryData([
    GET_NODE_ATTRIBUTES_DETAILS,
    templateId,
  ]) as INodeDetails;

  useEffect(() => {
    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      objectTemplateId,
    ]) as INodeDetails;

    if (nodeDetails?.templateId === TEMPLATES_ATTRIBUTE_TEMPLATE_ID) {
      setTemplateId(nodeDetails?.parentId.toString());
    } else setTemplateId(nodeDetails?.id.toString());
  }, [objectTemplateId]);

  const mutation = useMutation(addAttributes, {
    onSuccess: () => {
      dispatch(setWorkingVersionMask(false));
      setEditingAttribute(null);
    },
    onError: () => {
      showErrorNotification("Unable to add attributes!");
    },
  });

  function recursiveRemove(list, id, parent?: any) {
    return list
      ?.map((item) => {
        return { ...item };
      })
      ?.filter((item) => {
        if (item?.children) {
          item.children = recursiveRemove(item?.children, id, item);
          item.countChildren = item.children?.length;
        }

        if (parent && item.key == id && parent.children.length === 1) {
          parent.isLeaf = true;
        }
        return item.key != id;
      });
  }

  const updateNewId = (oldRandomId, newId) => {
    const selectedNode = searchRecursivelyByID(treeData, oldRandomId);
    const newTreeData = [...treeData];
    updateNewIdRecursively(newTreeData, oldRandomId, newId);
    setTreeData(newTreeData);

    if (selectedNode) {
      const previousData =
        (queryClient.getQueryData([
          GET_CHILDRENS,
          selectedNode?.parentId.toString(),
        ]) as any[]) || [];

      previousData.unshift({
        body: [],
        id: newId,
        last: true,
        name: selectedNode?.name,
        parentId: selectedNode?.parentId,
        templateId: selectedNode?.templateId,
      });
      queryClient.setQueryData(
        [GET_CHILDRENS, selectedNode.parentId.toString()],
        previousData
      );

      setSelected({
        keys: [newId],
        info: [
          {
            id: newId,
            isAsset: false,
            name: selectedNode?.name,
            parentId: selectedNode?.parentId,
            templateId: selectedNode?.templateId,
            body: selectedNode?.body,
            isLeaf: selectedNode?.isLeaf,
          },
        ],
      });
    }

    setTimeout(() => {
      dispatch(setWorkingVersionMask(false));
    }, 500);
  };

  const addNodeMutation = useMutation(addNodeService, {
    onSuccess: (newNodeId) => {
      updateNewId(selected.keys[0], newNodeId);

      setSpecialAttribute(false);
      setEditingAttribute(null);
      dispatch(setWorkingVersionMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save node!");
    },
  });

  const handleCancel = () => {
    if (isDraft) {
      const newData = recursiveRemove(treeData, selected.keys[0]);
      setTreeData([...newData]);

      if (newData.length > 0) {
        const selectedNode = newData[0];
        if (selectedNode) {
          setSelected({ info: [...selected.info], keys: [selectedNode?.key] });
        }
      }
    } else {
      queryClient.invalidateQueries([
        GET_NODE_ATTRIBUTES_DETAILS,
        selected.keys[0],
      ]);

      const bodyResponse = queryClient.getQueryData([
        GET_NODE_ATTRIBUTES_DETAILS,
        selected.keys[0],
      ]) as INodeDetails;

      const selectedTemplateAttributes =
        templatesData[bodyResponse?.templateId]?.attributeTemplates || [];

      const attributes = [];
      const mandatoryAttributes = [];
      selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
        const attributeValue = bodyResponse?.body?.find(
          (item) => item.id == attribute.id
        );
        if (attributeValue) {
          if (attribute?.mandatory) {
            mandatoryAttributes.push(attribute.id);
          }

          attributes.push({
            ...attributeValue,
            ...attribute,
            value:
              attribute.type === "switch"
                ? attributeValue?.value || false
                : attributeValue?.value,
          });
        }
      });

      setMandatoryAttributes([...mandatoryAttributes]);
      setAttributes([...attributes]);
    }
    setIsDraft(false);
    dispatch(setWorkingVersionMask(false));
  };

  const handleSave = () => {
    const allAttributes = JSON.parse(JSON.stringify([...attributes]));
    allAttributes.forEach((attr) => {
      if (attr.type === "relation") {
        attr.value = attr.value || [];
      }
      if (attr.type.startsWith("multiplicity")) {
        attr.value = attr.value?.text1 + ".." + attr.value?.text2;
      }
      if (attr.type === "dropdownItems") {
        const dropdownValue = {};
        attr.value?.forEach((value) => {
          dropdownValue[value.key] = value.name;
          if (value.default) {
            allAttributes.push({
              mandatory: false,
              name: "Default values",
              type: attr.type,
              value: { [value.key]: value.name },
              id: METAATTRIBUTE_ID_DEFAULT_VALUES,
            });
          }
        });
        attr.value = dropdownValue;
      }
      delete attr.mandatory;
      delete attr.help;
      delete attr.order;
      delete attr.items;
      delete attr.regex;
    });

    if (isDraft) {
      const selectedNode = searchRecursivelyByID(treeData, selected.keys[0]);

      if (selectedNode) {
        addNodeMutation.mutate({
          name: selectedNode.name,
          id: selectedNode.parentId,
          templateId: selectedNode.templateId,
          nodeType: "DATA",
          body: allAttributes,
        });
      }
    } else {
      mutation.mutateAsync({
        id: selected.keys[0],
        body: allAttributes,
        specialAttribute: specialAttribute,
      });
      setSpecialAttribute(false);
      dispatch(setWorkingVersionMask(false));
    }
    setIsDraft(false);
  };

  useEffect(() => {
    let disabled = false;
    attributes.forEach((item: any) => {
      const isEmpty =
        (item?.type === "switch" && item?.value === null) ||
        !item?.value ||
        item?.value?.length === 0 ||
        (item?.type === "dropdownItems" && (item?.value || []).length === 0);
      const invalidRegex =
        item?.regex && !new RegExp(item?.regex).test(item?.value);
      if (item?.type === "multiplicity") {
        if (item?.value?.text1 === "n" && item?.value?.text2 !== "n") {
          disabled = true;
        }
        if (
          Number(item?.value?.text1) !== null &&
          Number(item?.value?.text2) !== null &&
          Number(item?.value?.text1) > Number(item?.value?.text2)
        ) {
          disabled = true;
        }
        if (!item?.value?.text1 || !item?.value?.text2) {
          disabled = true;
        }
      }
      if ((mandatoryAttributes.includes(item.id) && isEmpty) || invalidRegex) {
        disabled = true;
      }
    });

    setDisabled(disabled);
  }, [attributes, workingVersionData]);

  const publishMutation = useMutation(publishWorkingVersion, {
    onSuccess: () => {
      notification.success({
        message: t("Success!"),
        description: t("Templates published successfully!"),
      });

      queryClient.invalidateQueries(GET_ALL_TEMPLATES_KEY);
      queryClient.invalidateQueries([GET_COUNTERS, templateId]);
      queryClient.invalidateQueries([GET_NODE_ATTRIBUTES_DETAILS, templateId]);
      if (
        !trashCollapsed &&
        selectedTrash.info[0]?.templateId == Number(templateId)
      ) {
        setTimeout(() => {
          queryClient.invalidateQueries([
            GET_NODE_ATTRIBUTES_DETAILS,
            selectedTrash.keys[0],
          ]);
        }, 500);
      }
      dispatch(setBottomNavbarOpen(false));
      dispatch(setSelectedBottomNavbar(""));

      setTimeout(() => {
        dispatch(setPublishWorkingVersion(true));
      }, 400);
    },
    onError: (error: any) => {
      if (error?.data?.error?.startsWith("The same same exists in node")) {
        showErrorNotification("Template with same name already exists!");
      } else showErrorNotification("Unable to publish templates!");
    },
  });

  const handlePublish = () => {
    publishMutation.mutate(workingVersionData?.id);
  };

  const handleDelete = () => {
    const bodyResponse = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      workingVersionData?.id?.toString(),
    ]) as any;

    setAction({
      id: workingVersionData?.id,
      label: bodyResponse?.name,
      key: "delete-working-version",
    });
  };

  const isDisabledTemplate = parentNodeDetails?.flag?.includes(DELETED_FLAG);

  const handleAttributeTemplateDelete = () => {
    const updatedTreeData = [...treeData];
    const childrens = updatedTreeData[0]?.children?.filter(
      (child) => !selected.keys.includes(child.key)
    );
    updatedTreeData[0].children = childrens;
    if (childrens.length === 0) {
      updatedTreeData[0].isLeaf = true;
    }
    setTreeData([...updatedTreeData]);
    if (updatedTreeData.length > 0) {
      setSelected({
        keys: [updatedTreeData[0].key],
        info: [
          {
            id: updatedTreeData[0].key,
            isAsset: false,
            name: updatedTreeData[0]?.name,
            parentId: updatedTreeData[0]?.parentId,
            templateId: updatedTreeData[0]?.templateId,
            body: updatedTreeData[0]?.body,
            isLeaf: updatedTreeData[0]?.isLeaf,
          },
        ],
      });
    }
  };

  const isDeleteWorkingVersionAction = action?.key === "delete-working-version";

  return (
    <Wrapper theme={theme}>
      {contextHolder}
      <TopBar theme={theme}>
        {!isDisabledTemplate && (
          <div className="right-div">
            {workingVersionActive ? (
              <>
                <Button
                  type="primary"
                  onClick={handleCancel}
                  className="cancel-button"
                >
                  {t("Cancel")}
                </Button>
                <Button
                  type="primary"
                  onClick={handleSave}
                  className="save-button"
                  disabled={disabled}
                  loading={addNodeMutation.isLoading || mutation.isLoading}
                >
                  {t("Save")}
                </Button>
              </>
            ) : (
              <>
                {/* !isNewTemplate && */}
                {globalPermissions.includes("DELETE") && (
                  <Button
                    type="primary"
                    onClick={handleDelete}
                    className="cancel-button"
                  >
                    {t("Delete")}
                  </Button>
                )}
                {globalPermissions.includes("PUBLISH") && (
                  <Button
                    type="primary"
                    className="publish save-button"
                    onClick={handlePublish}
                    disabled={disabled}
                    loading={publishMutation.isLoading}
                  >
                    {t("Publish")}
                  </Button>
                )}
              </>
            )}
          </div>
        )}
      </TopBar>

      {isDisabledTemplate && (
        <Alert
          message={t("Draft version of disabled templates cannot be edited!")}
          type="warning"
          closable
        />
      )}

      <div className="content">
        {isDisabledTemplate && (
          <div className="disabled-working-version-mask" />
        )}
        <ResizableDiv
          minWidth={18}
          resize="right"
          onResize={(_event, _direction, ref) => {
            if (ref.offsetWidth > 80) {
              setTreeWidth(ref.offsetWidth);
            }
          }}
          maxWidth={"100%"}
          width={treeWidth}
          defaultWidth="260px"
          saveWidthToLocalStorage
          stateKey={STATE_KEY}
          className={resizeCSS}
        >
          <WorkingVersionTree
            treeData={treeData}
            setAction={setAction}
            expandedKeys={expandedKeys}
            setExpandedKeys={setExpandedKeys}
            selected={selected}
            setSelected={setSelected}
            dropdownOpen={dropdownOpen}
            setTreeData={setTreeData}
            setDropdownOpen={setDropdownOpen}
          />
        </ResizableDiv>
        <WorkingVersionArea
          attributes={attributes}
          setAttributes={setAttributes}
          id={selected?.keys[0]}
          editingAttribute={editingAttribute}
          isDraft={isDraft}
          setEditingAttribute={setEditingAttribute}
          setMandatoryAttributes={setMandatoryAttributes}
          mandatoryAttributes={mandatoryAttributes}
          setSpecialAttribute={setSpecialAttribute}
          updateAllowedChildrens={(templateIds: number[]) => {
            const newTreeData = [...treeData];
            updateAllowedChildrens(
              newTreeData,
              templateIds,
              setAction,
              setDropdownOpen
            );

            setTreeData(newTreeData);
          }}
        />
      </div>

      {(action.key === "rename" ||
        action?.key?.startsWith("add-with-template")) && (
        <AddNodeModal
          nodeType={3}
          metamodel
          edit={action.key === "rename"}
          id={action.id}
          treeData={treeData}
          templateIds={action?.templateId}
          parentId={action?.parentId}
          afterSave={(tempGeneratedID, nodeName, selectedTemplateIcon) => {
            const updatedTree = [...treeData];
            updateChildNode(
              updatedTree,
              tempGeneratedID,
              nodeName,
              selectedTemplateIcon,
              action,
              []
            );
            setTreeData(updatedTree);

            if (action.key !== "rename") {
              dispatch(setWorkingVersionMask(true));
              setIsDraft(true);
              setSelected({
                info: [...selected.info],
                keys: [tempGeneratedID],
              });
            }
          }}
          label={action.label}
          isOpen={
            action.key === "rename" ||
            action?.key?.startsWith("add-with-template")
          }
          title={action.title}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}

      {action?.key === "delete" && (
        <DeleteAttributeTemplateModal
          isOpen={action?.key === "delete"}
          id={action.id}
          action={action}
          afterDelete={async () => {
            handleAttributeTemplateDelete();
          }}
          label={action.label}
          isMultiple={selected.keys.length > 1}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}
      {isDeleteWorkingVersionAction && (
        <DeleteWorkingVersion
          isOpen={isDeleteWorkingVersionAction}
          id={action.id}
          label={action.label}
          onClose={() => {
            setDropdownOpen(false);
            setAction({ key: null });
          }}
        />
      )}
    </Wrapper>
  );
};

export { WorkingVersion };

const resizeCSS = css`
  flex: unset !important;
  background-color: #fff !important;
  border-right: 1px solid #eee;

  & > .indicator {
    top: 48% !important;
  }
`;

const TopBar = styled.div<{ theme?: any }>`
  color: #fff;
  font-size: 12px;
  min-height: 32px;
  display: flex;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.metamodelBreadcrumbsColor};
  padding: 4px 10px;
  align-items: center;

  & button {
    font-size: 13px;
    height: 24px;
    padding: 0px 10px;
    border-radius: 3px;
    box-shadow: none;
  }
  & .right-div {
    display: flex;
    margin-left: auto;
    gap: 10px;

    & .publish {
      background-color: #056b05;

      &:hover {
        background-color: #056b05;
        opacity: 0.8;
      }

      &:disabled:hover {
        background-color: gray;
      }
    }

    & button {
      &:disabled {
        border: none;
        color: #fff;
        opacity: 0.7;
        background: gray;
      }
    }
  }
`;
const Wrapper = styled.div<{ theme: any }>`
  margin-top: 2px;
  height: 100%;

  & .ant-alert {
    font-size: 12px;
    margin: 10px;
    padding: 4px 12px;
  }

  & .tree {
    padding-top: 8px;
    padding-right: 8px;
  }
  & .disabled-working-version-mask {
    position: absolute;
    background: #80808012;
    height: 100%;
    width: 100%;
    inset: 0;
    z-index: 100;
  }

  & .mask {
    height: calc(100% + 0px);
    width: calc(100% + 11px);
    margin-left: -10px;
    margin-top: -10px;
  }

  & .ant-tree-title {
    width: 100%;
  }
  & .content {
    /* padding: 10px; */
    position: relative;
    display: flex;
    height: 100%;
  }

  & .ant-tree-switcher > svg {
    width: 20px;
    transition: all 0.2s ease-in;
    height: fit-content;

    & path {
      fill: ${({ theme }) => theme.colorPrimary};
    }
  }
`;
