import { styled } from "@linaria/react";
import { Dropdown, Modal, Tree, notification } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  saveLocalSettings,
  saveGlobalSettings,
  getLocalSettingsDetails,
  getSettingsDetails,
} from "../../../services";
import { AddGraphFilterModal } from "../../molecules";
import {
  GET_GLOBAL_SETTINGS_KEY,
  GET_LOCAL_SETTINGS_KEY,
} from "../../../constants";

const GraphFilterTree = ({
  selectedFilters,
  setSelectedFilters,
  allTemplates,
  filterOptions,
  setFilterOptions,
  onFilter,
}) => {
  const { t } = useTranslation();
  const [modal, contextHolder] = Modal.useModal();
  const queryClient = useQueryClient();

  const { data: localSettingsData } = useQuery(
    GET_LOCAL_SETTINGS_KEY,
    getLocalSettingsDetails,
    {
      cacheTime: Infinity,
    }
  );

  const { data: globalSettingsData } = useQuery<any>(
    GET_GLOBAL_SETTINGS_KEY,
    getSettingsDetails,
    {
      cacheTime: Infinity,
    }
  );

  const [filterExpandedKeys, setFilterExpandedKeys] = useState([]);
  const [saveAs, setSaveAs] = useState(null);
  const [addFilter, setAddFilter] = useState(null);

  const ADD_OPTION = [
    {
      key: "1",
      label: t("Add New"),
    },
  ];

  const FILTER_ACTIONS = [
    {
      key: "update",
      label: t("Update"),
    },
    {
      key: "save-as",
      label: t("Save As"),
    },
    {
      key: "delete",
      label: t("Delete"),
    },
  ];

  const handleFilterActions = ({ key }, filterType, id) => {
    switch (key) {
      case "update": {
        const allSettings = [
          ...(globalSettingsData?.body[0]?.value?.globalFilters || []),
        ];
        const index = allSettings.findIndex((item) => item.id === id);
        allSettings[index].selectedTemplateKeys = selectedFilters.templates;
        allSettings[index].displayOptions = selectedFilters.display;
        globalMutation.mutate({
          value: {
            ...(globalSettingsData?.body
              ? globalSettingsData?.body[0]?.value || {}
              : {}),
            globalFilters: allSettings,
          },
        });
        notification.success({
          message: t("Success!"),
          description: t("Filter updated successfully!"),
        });
        return;
      }
      case "save-as": {
        setSaveAs(id);
        return;
      }
      case "delete": {
        modal.confirm({
          title: t("Delete?"),
          content: t("Are you sure to delete this filter?"),
          onOk: () => {
            const data =
              filterType === "global" ? globalSettingsData : localSettingsData;

            const key = filterType === "global" ? "globalFilters" : "myFilters";

            const settings = (data?.body[0]?.value?.[key] || []).filter(
              (item) => item.id !== id
            );
            const value = {
              ...(data?.body ? data?.body[0]?.value || {} : {}),
              [key]: settings,
            };

            if (filterType === "global") {
              globalMutation.mutate({
                value: value,
              });
            } else {
              localMutation.mutate({
                value: value,
              });
            }

            setSelectedFilters({
              ...selectedFilters,
              filters: ["display-all"],
              templates: [...allTemplates],
            });
            queryClient.invalidateQueries(
              filterType === "global"
                ? GET_GLOBAL_SETTINGS_KEY
                : GET_LOCAL_SETTINGS_KEY
            );
          },
        });
      }
    }
  };

  useEffect(() => {
    const displayFilters = [
      {
        title: t("Display All"),
        key: "display-all",
      },
      {
        title: (
          <Dropdown
            menu={{
              items: ADD_OPTION,
              onClick: () => handleFilterContextMenuClick("myFilters"),
            }}
            trigger={["contextMenu"]}
          >
            <span>My filters</span>
          </Dropdown>
        ),
        key: "my-filters",
        children: [],
      },
      {
        title: (
          <Dropdown
            menu={{
              items: ADD_OPTION,
              onClick: () => handleFilterContextMenuClick("global"),
            }}
            trigger={["contextMenu"]}
          >
            <span>{t("Global filters")}</span>
          </Dropdown>
        ),
        key: "global-filters",
        children: [],
      },
    ];
    if (displayFilters.length > 0) {
      if (localSettingsData?.body[0]?.value.myFilters) {
        const childrens = [];
        localSettingsData.body[0]?.value.myFilters?.forEach((filter) => {
          childrens.push({
            id: filter.id,
            key: filter.id,
            type: "myFilters",
            title: (
              <Dropdown
                onOpenChange={(open) => {
                  if (open && selectedFilters.filters[0] !== filter.id) {
                    const selectedFilter =
                      localSettingsData?.body[0]?.value?.myFilters?.find(
                        (item) => item.id === filter.id
                      );

                    if (selectedFilter) {
                      const filteredTemplates = allTemplates.filter(
                        (value) =>
                          !selectedFilter?.selectedTemplateKeys?.includes(value)
                      );

                      setSelectedFilters({
                        ...selectedFilters,
                        filters: [filter.id],
                        templates: [...filteredTemplates],
                      });
                    }
                  }
                }}
                menu={{
                  items: FILTER_ACTIONS,

                  onClick: (e) =>
                    handleFilterActions(e, "myFilters", filter.id),
                }}
                trigger={["contextMenu"]}
              >
                <span>{filter.name}</span>
              </Dropdown>
            ),
          });
        });
        displayFilters[1].children = childrens;
      }

      if (globalSettingsData?.body[0]?.value.globalFilters) {
        const childrens = [];
        globalSettingsData.body[0]?.value.globalFilters?.forEach((filter) => {
          childrens.push({
            id: filter.id,
            key: filter.id,
            type: "global",
            title: (
              <Dropdown
                onOpenChange={(open) => {
                  if (open && selectedFilters.filters[0] !== filter.id) {
                    const selectedFilter =
                      globalSettingsData?.body[0]?.value?.globalFilters?.find(
                        (item) => item.id === filter.id
                      );

                    if (selectedFilter) {
                      const filteredTemplates = allTemplates.filter(
                        (value) =>
                          !selectedFilter?.selectedTemplateKeys?.includes(value)
                      );

                      setSelectedFilters({
                        ...selectedFilters,
                        filters: [filter.id],
                        templates: [...filteredTemplates],
                      });
                    }
                  }
                }}
                menu={{
                  items: FILTER_ACTIONS,

                  onClick: (e) => handleFilterActions(e, "global", filter.id),
                }}
                trigger={["contextMenu"]}
              >
                <span>{filter.name}</span>
              </Dropdown>
            ),
          });
        });
        displayFilters[2].children = childrens;
      }
    }

    setFilterOptions({ ...filterOptions, filters: [...displayFilters] });
  }, [
    globalSettingsData,
    localSettingsData,
    allTemplates,
    selectedFilters.filters,
    selectedFilters.display,
  ]);

  const handleFilterContextMenuClick = (key) => {
    setAddFilter(key);
  };

  const globalMutation = useMutation(saveGlobalSettings, {
    onSuccess: () => {
      queryClient.invalidateQueries(GET_GLOBAL_SETTINGS_KEY);
    },
  });

  const localMutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
    },
  });

  const getFilteredTemplates = () => {
    return allTemplates.filter(
      (item) => !selectedFilters.templates?.includes(item)
    );
  };

  const handleFilterClick = (selectedKey, nodeInfo) => {
    if (selectedKey[0] === "display-all") {
      onFilter([]);
      setSelectedFilters({
        ...selectedFilters,
        filters: selectedKey,
        templates: [...allTemplates],
        appliedFilter: [],
      });
      return;
    }

    const settings =
      nodeInfo?.node?.type === "global"
        ? globalSettingsData
        : localSettingsData;

    const settingKey =
      nodeInfo?.node?.type === "global" ? "globalFilters" : "myFilters";

    setSelectedFilters({ ...selectedFilters, filters: selectedKey });
    const selectedFilter = settings?.body[0]?.value?.[settingKey]?.find(
      (item) => item.id === selectedKey[0]
    );

    if (selectedFilter) {
      const filteredTemplates = allTemplates.filter(
        (value) => !selectedFilter?.selectedTemplateKeys?.includes(value)
      );

      setSelectedFilters({
        ...selectedFilters,
        filters: selectedKey,
        templates: [...filteredTemplates],
        display: selectedFilter.displayOptions,
        appliedFilter: selectedFilter?.selectedTemplateKeys,
      });
      onFilter(selectedFilter?.selectedTemplateKeys);
    }
  };

  return (
    <Wrapper>
      <div className="title">{t("Filters")}</div>
      <Tree
        selectedKeys={selectedFilters.filters}
        showLine
        expandedKeys={filterExpandedKeys}
        onSelect={handleFilterClick}
        treeData={filterOptions.filters}
        onExpand={(expandedKeys) => setFilterExpandedKeys(expandedKeys)}
      />
      {contextHolder}

      {(!!addFilter || !!saveAs) && (
        <AddGraphFilterModal
          isOpen={!!addFilter || !!saveAs}
          filterType={addFilter}
          saveAs={saveAs}
          onClose={() => {
            setAddFilter(null);
            setSaveAs(null);
          }}
          selectedTemplateKeys={getFilteredTemplates()}
          displayOptions={selectedFilters.display}
          afterSave={(id, filterType) => {
            setSelectedFilters({ ...selectedFilters, filters: [id] });
            setFilterExpandedKeys([
              ...filterExpandedKeys,
              filterType === "global" ? "global-filters" : "my-filters",
            ]);
          }}
        />
      )}
    </Wrapper>
  );
};

export { GraphFilterTree };

const Wrapper = styled.div`
  & .title {
    background-color: #ccdaec;
    padding: 3px 10px;
    color: #4277a2;
  }
`;
