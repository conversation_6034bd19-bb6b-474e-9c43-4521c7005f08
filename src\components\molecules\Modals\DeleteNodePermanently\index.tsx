import { styled } from "@linaria/react";
import { Button, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import {
  deleteMultipleNodeService,
  deleteNodeService,
} from "../../../../services/node";
import { useEffect, useRef } from "react";
import {
  GET_CHILDRENS,
  GET_LOCAL_SETTINGS_KEY,
  TRASHCAN_PARENT_NODE_ID,
  getAttributeIcon,
} from "../../../../constants";
import { captureException } from "@sentry/react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { ILocalSettings } from "../../../../interfaces";
import { saveLocalSettings } from "../../../../services";
import { setPinned } from "../../../../store/features/pinned";
import { Dialog } from "primereact/dialog";

const DeleteNodePermanently = ({
  onClose,
  isOpen,
  onDeleteClick,
  afterDelete,
  isMultiple,
  action,
}: Props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const ref = useRef<any>();

  const { id, templateId, label, isLeaf } = action;

  const { selectedTrash } = useSelector((state: RootState) => state.trash);
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );
  const pinned = useSelector((state: RootState) => state.pinned.pinned);
  const dispatch = useDispatch();

  const settingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    ref?.current?.focus();
  }, [isOpen]);

  const mutation = useMutation(
    isMultiple ? deleteMultipleNodeService : deleteNodeService,
    {
      onSuccess: () => {
        afterDelete && afterDelete();
        const newPinned = [];
        let toUpdatePinned = false;
        pinned?.forEach((pin) => {
          if (!selectedTrash.keys.includes(pin.id)) {
            newPinned.push({ ...pin });
          } else {
            toUpdatePinned = true;
          }
        });

        if (toUpdatePinned) {
          updatePinned(newPinned);
        }

        queryClient.invalidateQueries([GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID]);

        notification.success({
          message: t("Success!"),
          description: t("Node deleted successfully!"),
        });

        onClose();
      },
      onError: (e: any) => {
        notification.error({
          message: "Error in deleting!",
          description: e?.data?.error || "Please try again after sometime",
        });
        captureException(e?.data?.error);
      },
    }
  );

  const pinnedMutation = useMutation(saveLocalSettings);

  const updatePinned = (newPinned) => {
    pinnedMutation.mutateAsync({
      value: {
        ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
        pinned: newPinned,
      },
    });
    dispatch(setPinned([...newPinned]));
  };

  const handleDelete = () => {
    if (onDeleteClick) {
      onDeleteClick();
      onClose();
    } else {
      mutation.mutate(isMultiple ? { keys: selectedTrash.keys } : { id: id });
    }
  };

  let templateIcon = null;
  if (!isMultiple) {
    templateIcon = templatesData[Number(templateId)]?.icon || "_30_folder";
  }

  return (
    <Dialog
      visible={isOpen}
      onHide={onClose}
      footer={null}
      className="export-modal draggable-modal"
      header={isMultiple ? `${t("Delete selected")}?` : `${t("Delete")}`}
    >
      <Wrapper>
        {!isMultiple && (
          <p>
            {getAttributeIcon(templateIcon)} {label}
          </p>
        )}

        <h5>
          {isMultiple ? (
            t(
              "Are you sure you want to delete these selected items permanently?"
            )
          ) : (
            <>
              {t("Are you sure you want to delete the item permanently")}?
              {!isLeaf && (
                <span>
                  {t("Be minded that the entire branch will be moved")}
                </span>
              )}
            </>
          )}
        </h5>

        <Button
          onClick={handleDelete}
          type="primary"
          ref={ref}
          loading={mutation.isLoading}
        >
          {isMultiple ? t("Delete all") : t("Delete")}
        </Button>
      </Wrapper>
    </Dialog>
  );
};

export { DeleteNodePermanently };

interface Props {
  onClose: () => void;
  isOpen: boolean;
  onDeleteClick?: () => void;
  afterDelete?: () => void;
  isMultiple?: boolean;
  selectedKeys?: number[];
  hasAssetTypeNode?: boolean;
  templateId?: number;
  action: {
    label: string;
    id: string;
    templateId?: number;
    isLeaf: boolean;
  };
}

const Wrapper = styled.form`
  padding-bottom: 20px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;

  & img {
    object-fit: contain;
  }

  & h5 {
    font-size: 13px;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 6px;
    text-align: center;

    & span {
      color: #949494;
      font-size: 12px;
      display: block;
    }
  }

  & p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4277a2;

    & svg {
      width: 20px;
      height: 20px;
    }
  }
  & button {
    margin-left: auto;
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }
`;
