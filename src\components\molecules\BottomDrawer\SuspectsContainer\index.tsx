import { styled } from "@linaria/react";
import { useTheme } from "../../../../utils/useTheme";
import { StyledDataTable } from "../../../organisms";
import { ReactComponent as CompareIcon } from "../../../../assets/comparison.svg";
import { GET_SETTINGS_VARIABLE, GET_SUSPECTS } from "../../../../constants";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useNotification } from "../../../../utils/functions/customHooks";
import { setBottomDrawerMask } from "../../../../store/features";
import { getSuspectsList, saveSuspectDecision } from "../../../../services";
import { getInitialTableFilters } from "../../../../utils/functions/getInitialTableFilters";
import { transformObjectPath } from "../../../../utils";
import { Button, Collapse, Flex, Tooltip } from "antd";
import { confirmPopup, ConfirmPopup } from "primereact/confirmpopup";
import { CompareNodes } from "./CompareNodes";
import { RootState } from "../../../../store";
import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../../store/features/navigation";
import { AttributeItem } from "../../../atoms";

const TITLE_CLASSNAME = "suspects-attributes";

const SuspectsContainer = ({ id }) => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const [selected, setSelected] = useState([]);
  const [compare, setCompare] = useState(null);
  const [data, setData] = useState([]);
  const [changeDetected, setChangeDetected] = useState(false);
  const [triggerChange, setTriggerChange] = useState(0);
  const [sort, setSort] = useState({
    field: null,
    order: null,
  });

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  // fetching suspects
  const { data: suspectsData, isLoading } = useQuery<any>(
    [GET_SUSPECTS, id],
    () => getSuspectsList(id)
  );

  useEffect(() => {
    if (suspectsData) {
      setData(
        [...suspectsData]?.map((suspects) => {
          return { ...suspects, decision: "pending" };
        })
      );
      setTriggerChange((trigger) => trigger + 1);
    }
  }, [suspectsData]);

  const generateSuspects = (suspectsList, id) => {
    const suspects = [];

    suspectsList?.forEach((suspect) => {
      suspects.push({
        key: suspect.id,
        label: suspect?.name,
        children: (
          <Flex dir="column">
            <div style={{ width: "100%" }}>
              {suspect?.body?.length === 0 ? (
                <p className="no-attribute">{t("No attributes")}</p>
              ) : (
                suspect?.body?.map((attribute) => {
                  return (
                    <AttributeItem
                      key={attribute.id}
                      readOnly
                      {...attribute}
                      title={attribute.name}
                      titleClassName={TITLE_CLASSNAME}
                    />
                  );
                })
              )}
              <Button
                type="primary"
                onClick={() => {
                  const updatedData = data?.map((_row) => {
                    if (_row?.id === id)
                      return {
                        ..._row,
                        renameTo: suspect.id,
                        decision: `Rename to ${suspect.name}`,
                      };

                    return { ..._row };
                  });
                  setData([...updatedData]);
                  dispatch(setBottomDrawerMask(true));
                  setChangeDetected(true);
                }}
              >
                {t("Rename")}
              </Button>
            </div>
          </Flex>
        ),
      });
    });
    return suspects;
  };

  const COLUMNS = [
    {
      label: t("Name"),
      key: "name",
    },
    {
      label: t("Template"),
      key: "templateId",
      width: 100,
      render: (record) => {
        const selectedTemplate = templatesData[Number(record.templateId)];
        if (selectedTemplate) {
          return selectedTemplate.name;
        }
        return "-";
      },
    },
    {
      label: t("Path"),
      key: "pathName",
      width: 100,
      render: (value) => transformObjectPath(value?.pathName, false),
    },
    {
      label: t("Suspects"),
      key: "suspects",
      width: 300,
      render: (record) => {
        return (
          <Flex vertical>
            <Collapse
              ghost
              items={generateSuspects(record?.children, record?.id)}
              expandIcon={({ isActive }) => (
                <i
                  className={
                    isActive ? "pi pi-angle-down" : "pi pi-angle-right"
                  }
                />
              )}
            />
            <Button
              className="not-found"
              onClick={() => {
                const updatedData = data?.map((_row) => {
                  if (_row?.id === record?.id)
                    return { ..._row, decision: "Not Found" };

                  return { ..._row };
                });

                setData([...updatedData]);
                dispatch(setBottomDrawerMask(true));
                setChangeDetected(true);
              }}
            >
              {t("Mark as not found")}
            </Button>
          </Flex>
        );
      },
    },
    {
      label: t("Decision"),
      key: "decision",
      width: 100,
      render: (record) => <p className="decision">{record?.decision}</p>,
    },

    {
      label: "",
      key: "actions",
      render: (record) => {
        return (
          <div className="compare">
            <Tooltip title="Compare & Decide">
              <div
                style={{ width: "fit-content" }}
                onClick={() => setCompare(record)}
              >
                <CompareIcon />
              </div>
            </Tooltip>
          </div>
        );
      },
    },
  ];
  const [filters, setFilters] = useState(getInitialTableFilters(COLUMNS));

  const handleCancel = () => {
    setTriggerChange((prev) => (!prev ? 1 : prev + 1));

    dispatch(setBottomDrawerMask(false));
    setChangeDetected(false);
    if (suspectsData) {
      setData(
        [...suspectsData]?.map((suspects) => {
          return { ...suspects, decision: "pending" };
        })
      );
    }
  };

  const mutation = useMutation(saveSuspectDecision, {
    onSuccess: () => {
      showSuccessNotification("Suspects updated successfully!");
      queryClient.invalidateQueries([GET_SUSPECTS, id]);
      dispatch(setBottomDrawerMask(false));
      setChangeDetected(false);
      queryClient.invalidateQueries(GET_SETTINGS_VARIABLE);
      dispatch(setBottomNavbarOpen(false));
      dispatch(setSelectedBottomNavbar(""));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
      setChangeDetected(false);
    },
  });

  const handleSave = () => {
    const decisionsForSuspectList = [];
    data?.forEach((suspect) => {
      if (suspect?.decision === "pending") {
        return;
      }
      decisionsForSuspectList.push({
        suspectNodeId: suspect.id,
        newNodeId: suspect?.decision === "Not Found" ? null : suspect?.renameTo,
      });
    });

    mutation.mutate({
      extractorId: Number(id),
      decisionsForSuspectList: decisionsForSuspectList,
    });
  };

  const rowClassName = (data) => {
    if (data?.decision === "pending") {
      return "row-pending";
    }
    if (data?.decision === "Not Found") {
      return "row-deleted";
    }
    if (data?.decision?.startsWith("Rename")) {
      return "row-renamed";
    }
    return "";
  };

  return (
    <Wrapper
      theme={theme}
      style={{ border: changeDetected ? "1px solid red" : "none" }}
    >
      {contextHolder}
      <StyledDataTable
        height={"100%"}
        loading={isLoading}
        globalFilterFields={["nodeName"]}
        noDownload={selected.length === 0}
        excelFileName="suspects"
        extra={
          <div className="extra-wrapper">
            <ConfirmPopup />

            {selected?.length > 0 && (
              <Button
                type="primary"
                className="cancel-button"
                onClick={(event) =>
                  confirmPopup({
                    target: event.currentTarget,
                    message: t(
                      "Are you sure you want to mark the selected nodes as not found?"
                    ),
                    icon: "pi pi-exclamation-triangle",

                    defaultFocus: "accept",
                    accept: () => {
                      const selectedNodesId = selected?.map(
                        (_selected) => _selected.id
                      );
                      const updatedData = data?.map((_row) => {
                        if (selectedNodesId?.includes(_row?.id)) {
                          return { ..._row, decision: "Not Found" };
                        }
                        return { ..._row };
                      });
                      setChangeDetected(true);
                      setData([...updatedData]);
                      setSelected(null);
                      dispatch(setBottomDrawerMask(true));
                    },
                  })
                }
              >
                {t("Mark as not found")}
              </Button>
            )}
          </div>
        }
        data={data}
        customRowClassName={rowClassName}
        columns={COLUMNS}
        disableSave={data?.some((suspects) => suspects?.decision === "pending")}
        emptyMessage="No Suspects"
        selected={selected}
        setSelected={setSelected}
        triggerChange={triggerChange}
        // setAllColumnsRequest={setColumnsRequest}
        onCancelClick={handleCancel}
        saveLoading={mutation.isLoading}
        onSaveClick={handleSave}
        filters={filters}
        setFilters={setFilters}
        displaySaveCancel={changeDetected}
        sort={sort}
        setSort={setSort}
      />
      {!!compare && (
        <CompareNodes
          record={compare}
          onRename={(id, name) => {
            const updatedData = data?.map((_row) => {
              if (_row?.id === compare?.id)
                return { ..._row, renameTo: id, decision: `Rename to ${name}` };

              return { ..._row };
            });
            setData([...updatedData]);
            setCompare(null);
            dispatch(setBottomDrawerMask(true));
            setChangeDetected(true);
          }}
          onNotFound={() => {
            const updatedData = data?.map((_row) => {
              if (_row?.id === compare?.id)
                return { ..._row, decision: "Not Found" };

              return { ..._row };
            });
            setData([...updatedData]);
            setCompare(null);
            dispatch(setBottomDrawerMask(true));
            setChangeDetected(true);
          }}
          onHide={() => setCompare(null)}
          visible={!!compare}
        />
      )}
    </Wrapper>
  );
};

export { SuspectsContainer };

const Wrapper = styled.div<{ theme: any }>`
  height: 100%;
  overflow: auto;
  display: flex;
  width: 100%;
  padding: 10px;

  & .not-found {
    font-size: 12px;
    margin-top: 4px;
    border: 1px solid #8f0000;
    color: #8f0000;
  }

  & .no-attribute {
    font-size: 13px;
    color: #7f7f7f;
    margin-top: -11px;
  }
  & .ant-collapse {
    width: 100%;
  }

  & .ant-collapse-item {
    background: #fff;
    margin-bottom: 5px;
    border-radius: 7px;
  }

  & .ant-collapse button {
    font-size: 12px;
    height: auto;
    margin-top: 10px;
    background: #fff;
    box-shadow: none;
    color: #094375;
    border: 1px solid;
    width: 100%;
  }
  & .ant-collapse-content-box {
    padding: 7px 13px;
  }

  & .ant-collapse-header-text {
    font-size: 13px;
  }
  & .ant-collapse-header {
    padding: 7px;
    border-radius: 7px;
  }

  & button:disabled {
    color: #f4f4f4;
    opacity: 0.8;
  }

  & button.cancel-button {
    border-radius: 30px;
  }

  & .row-deleted {
    & .decision {
      color: #9d2323;
      font-weight: 500;
    }
  }

  & .row-renamed {
    & .decision {
      color: #0a680a;
      font-weight: 500;
    }
  }
  & .row-pending {
    background-color: #f4f4f4;

    & td {
      color: #656565;
    }

    & .decision {
      font-style: italic;
    }
  }
  & .compare svg {
    cursor: pointer;
    height: 20px;
    & path {
      fill: #4277a2;
    }
  }
  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & td.attributes-data {
    overflow: auto;
  }
  & .item {
    display: flex;
    align-items: stretch;
    gap: 6px;
    border: 0.5px solid #eaeaea;
    border-radius: 4px;
    margin-bottom: 6px;
    min-width: fit-content;
    & h6 {
      font-size: 13px;
      word-break: break-all;
      font-weight: 400;
      min-width: 60px;
      background-color: ${({ theme }) => theme.bgAttributes};
      border-right: 1px solid #eaeaea;
      width: 28%;
      text-align: left;
      max-width: 28%;
      color: ${({ theme }) => theme.colorPrimary};
      padding: 6px 10px;
      display: flex;
      align-items: center;
    }
    & p {
      padding: 6px;
      font-size: 13px;
      word-break: break-all;
      flex: 1;
      text-align: left;
      min-width: 60px;
      overflow: auto;
    }
  }

  & .ant-table-header > table,
  .ant-table-body > table {
    width: fit-content !important;
  }

  & td {
    cursor: auto !important;
  }

  & .table-wrapper {
    width: 100%;
  }

  & table {
    height: auto !important;
  }
  position: relative;
  & .anticon-export {
    font-size: 17px;
    margin-left: 10px;
  }
  & .ant-avatar {
    border-radius: 10px;
  }
`;
