import { Dialog } from "primereact/dialog";
import { useTheme } from "../../../../utils";
import { css } from "@linaria/core";
import { ReactNode } from "react";

interface Props {
  open: boolean;
  onCancel: () => void;
  title: any;
  children: ReactNode;
  footerContent?: ReactNode;
  size?: any;
  contentClassName?: string;
  customHeaderStyles?: string;
}

const StyledModal = ({
  onCancel,
  title,
  open,
  children,
  footerContent,
  size,
  contentClassName,
  customHeaderStyles,
}: Props) => {
  const theme = useTheme() as any;

  return (
    <Dialog
      visible={open}
      onHide={onCancel}
      maximizable
      style={size || { height: "500px", width: "800px" }}
      header={title}
      footer={footerContent}
      headerClassName={`${headerStyles} ${customHeaderStyles}`}
      headerStyle={
        {
          "--bgLight": theme.bgLight,
          "--bg-primary": theme.colorPrimary,
          "--bg-secondary": theme.colorSecondary,
        } as any
      }
      contentClassName={`${contentStyles} ${contentClassName}`}
    >
      {children}
    </Dialog>
  );
};

export { StyledModal };

const contentStyles = css`
  padding: 16px 20px;
`;

const headerStyles = css`
  padding: 8px 18px;
  background-color: var(--bgLight);

  & .p-dialog-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--bg-primary);
  }
  & button {
    color: var(--bg-primary);
  }
`;
