import { memo } from "react";
import type { CustomCellEditorProps } from "ag-grid-react";
import { TinyEditor } from "../../atoms";

export default memo(({ value, onValueChange }: CustomCellEditorProps) => {
  return (
    <div
      tabIndex={1}
      style={{ height: 300, width: "95%", overflow: "auto", padding: 10 }}
    >
      <TinyEditor
        minimal
        noFocus
        value={value}
        onEditorChange={(value, editor) => {
          const content = editor.getContent({ format: "text" });

          if (content?.length >= 18) {
            return;
          }
          onValueChange(value);
        }}
      />
    </div>
  );
});
