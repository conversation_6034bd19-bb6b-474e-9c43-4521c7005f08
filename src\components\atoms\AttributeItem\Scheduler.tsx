import { styled } from "@linaria/react";
import { useSchedule } from "../../../utils/functions/customHooks/useSchedule";
import { MyTable } from "../../organisms";
import { useEffect, useState } from "react";
import dayjs from "dayjs";

// attributeValue?.map((schedule, index) => (
//   <Schedule key={index}>
//     <div className="checkbox">
//       {schedule.enabled ? (
//         <i className="pi pi-check" />
//       ) : (
//         <i className="pi pi-times" />
//       )}{" "}
//     </div>
//     {getSchedule(schedule)}
//   </Schedule>
const Scheduler = ({ attributeValue }) => {
  const { getSchedule } = useSchedule();
  const [data, setData] = useState([]);

  const COLUMNS = [
    {
      headerName: "",
      field: "enabled",
      isAction: true,
      width: 60,
      maxWidth: 60,
      cellRenderer: ({ data }) => {
        return (
          <div className={data.enabled ? "enabled-check" : "disabled-check"}>
            {data.enabled ? (
              <i className="pi pi-check" />
            ) : (
              <i className="pi pi-times" />
            )}
          </div>
        );
      },
    },
    {
      headerName: "Start at",
      field: "startAt",
      flex: 1,
      minWidth: 100,
    },
    {
      headerName: "Repeat",
      field: "repeat",
      flex: 1,
      minWidth: 100,
    },
    {
      headerName: "Until",
      field: "until",
      flex: 1,
      minWidth: 100,
    },
    {
      headerName: "Set by",
      field: "user",

      flex: 1,
      minWidth: 100,
    },
  ];

  useEffect(() => {
    if (!attributeValue) {
      return;
    }

    const schedules = [];
    attributeValue?.forEach((schedule, index) => {
      schedules.push({
        id: index,
        user: "-",
        enabled: schedule.enabled,
        until: schedule.endAt
          ? dayjs(schedule.endAt).format("YYYY-MM-DD HH:mm")
          : "-",
        startAt: dayjs(schedule.startAt).format("YYYY-MM-DD HH:mm"),
        repeat:
          schedule.cronExpressionValues?.length > 0
            ? getSchedule(schedule)
            : "one-time",
      });
    });
    setData(schedules);
  }, [attributeValue]);

  return (
    <Wrapper>
      <MyTable noHeader noSelect columns={COLUMNS} data={data} />
    </Wrapper>
  );
};

export { Scheduler };

const Wrapper = styled.div`
  & .enabled-check {
    width: 20px;
    height: 20px;
    display: flex;
    margin: auto;
    border: 1px solid green;
    color: green;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
  }
  & .disabled-check {
    width: 20px;
    height: 20px;
    display: flex;
    margin: auto;
    border: 1px solid red;
    color: red;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
  }
`;
