import { Tree } from "antd";
import { useQueryClient } from "react-query";
import { validateSidebarDrop } from "../../../utils/functions/customHooks/useSidebarDragActions";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { ReactComponent as DownIcon } from "../../../assets/mdi_caret.svg";
import { getAllNodes } from "../../../services/node";

import { SidebarTreeLabel } from "../../atoms";
import type { DataNode, EventDataNode } from "antd/es/tree";
import { TEMP_GROUPING_NODE_ID } from "../../../constants";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  setExpandedKeys,
  setFocusedNode,
  setSelected,
  setBreadcrumb,
  setMoveToFocused,
} from "../../../store/features/sidebar";
import { ITreeData } from "../../../interfaces";
import {
  useFlags,
  useNotification,
  useParentHeight,
  usePermissions,
  useSidebarDragActions,
  useTemplateActions,
} from "../../../utils/functions/customHooks";
import { useEffect, useRef, useState, useCallback } from "react";
import { setTempBreadcrumbs } from "../../../store/features";
import { deepClone } from "../../../utils";

const SidebarTree = ({
  setAction,
  setDropdownOpen,
  dropdownOpen,
  treeData,
  setTreeData,
  fromDQM,
  setTempTreeData,
  tempTreeData,
  disabled,
  handleSort,
}) => {
  const treeRef = useRef(null);
  const treeComponentRef = useRef(null);
  const params = useParams();
  const location = useLocation();
  const queryClient = useQueryClient();
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );
  const nodeId = params?.nodeId;
  const metamodel =
    location.pathname.includes("metamodel") ||
    location.pathname.includes("data-sources");
  const [searchParams, setSearchParams] = useSearchParams();
  const { handleSidebarDrag } = useSidebarDragActions();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { ref: containerRef, height: containerHeight } = useParentHeight();

  const { getPermissions } = usePermissions();
  const { getFlags } = useFlags();
  const { getTemplateIcon, getTemplateName, getAllowedChildrens } =
    useTemplateActions();

  const [isExpanding, setIsExpanding] = useState(false);
  const [scrollToNode, setScrollToNode] = useState(false);

  // State for drag visual indicator
  const [dragPosition, setDragPosition] = useState<null | {
    key: React.Key;
    dropToGap?: boolean;
    dropPosition?: number;
  }>(null);
  const [validationStatus, setValidationStatus] = useState<null | {
    key: React.Key;
    status: "valid" | "invalid" | null;
  }>(null);

  const { contextHolder, showInfoNotification } = useNotification();
  const { selected, expandedKeys, focusedNode, moveToFocused } = useSelector(
    (state: RootState) => state.sidebar
  );

  const tempBreadcrumbs = useSelector(
    (state: RootState) => state.breadcrumbs.tempBreadcrumbs
  );

  const parentBreadcrumbs = useSelector(
    (state: RootState) => state.breadcrumbs.parentBreadcrumbs
  );

  const breadcrumb = useSelector(
    (state: RootState) => state.sidebar.breadcrumb
  );

  const movingMask = useSelector((state: RootState) => state.mask.movingMask);

  // Merge children by key, preserving any existing children not present in the new data
  const mergeChildren = (existing = [], incoming = []) => {
    const existingMap = new Map(existing.map((child) => [child.key, child]));
    const merged = incoming.map((child) => {
      const existingChild = existingMap.get(child.key);
      if (existingChild) {
        // Recursively merge children
        return {
          ...existingChild,
          ...child,
          children: mergeChildren(existingChild.children, child.children),
        };
      }
      return child;
    });
    // Optionally, add any existing children not present in incoming
    incoming.forEach((child) => existingMap.delete(child.key));
    return merged.concat(Array.from(existingMap.values()));
  };

  const updateTreeData = (
    list: DataNode[],
    key: React.Key,
    children: DataNode[]
  ): DataNode[] =>
    list.map((node) => {
      if (node.key === key) {
        return {
          ...node,
          // Merge new children with any existing children
          children: mergeChildren(node.children, children),
        };
      }
      if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children),
        };
      }
      return node;
    });

  const onLoadData = ({ key, children, breadcrumb }: any) =>
    new Promise<void>(async (resolve) => {
      if (children.length > 0) {
        setIsExpanding(false);
        resolve();
        return;
      }

      try {
        setIsExpanding(true);

        const data = await getAllNodes(key);

        const parentNodes = [];

        data?.forEach((value: ITreeData) => {
          parentNodes.push({
            key: value.id,
            parentId: key,
            body: value?.body,
            name: value.name,
            title: getTemplateName(value.templateId),
            icon: getTemplateIcon(value.templateId),
            templateId: value.templateId,
            isLeaf: value?.countChildren === 0,
            visualOrder: value?.visualOrder,
            flag: getFlags(value?.bitFlag),
            children: [],
            countChildren: value?.countChildren,
            permissionsId: value?.permissionsId,
            allowedChildren:
              value?.templateId == TEMP_GROUPING_NODE_ID
                ? value?.body?.find((body) => body?.type === "allowedChildren")
                    ?.value
                : [],

            breadcrumb: [
              ...breadcrumb,
              {
                id: value?.id,
                title: value?.name,
                parentId: key,
                templateName: getTemplateName(value.templateId),
                templateId: value.templateId,
                allowedChildrens:
                  value?.templateId == TEMP_GROUPING_NODE_ID
                    ? value?.body?.find(
                        (body) => body?.type === "allowedChildren"
                      )?.value || []
                    : getAllowedChildrens(value.templateId),
              },
            ],
          });
        });

        if (movingMask) {
          setTempTreeData(updateTreeData(tempTreeData, key, parentNodes));
        }

        setTreeData(updateTreeData(treeData, key, parentNodes));
        dispatch(setExpandedKeys([...expandedKeys, Number(key)]));
        setIsExpanding(false);

        if (searchParams.get("isSorting")) {
          const newParams = new URLSearchParams(searchParams);
          newParams.set("sort", searchParams.get("isSorting"));
          newParams.delete("isSorting");
          setSearchParams(newParams);
        }
        resolve();
      } catch (e) {
        setIsExpanding(false);
        resolve();
      }
    });

  const getRecursiveSelectedKeys = (
    selectedNodes,
    selectedKeys,
    selectedKeysInfo
  ) => {
    selectedNodes.forEach((item) => {
      if (!selectedKeys.includes(item.key)) {
        selectedKeys.push(item.key);
        selectedKeysInfo.push({
          id: item.key,
          parentId: item.parentId,
          name: item.name,
          isAsset: item.templateId === 2,
          templateId: item?.templateId,
          body: item?.body,
          isLeaf: item?.isLeaf,
          flag: getFlags(item?.bitFlag),
        });
      }

      if (item.children && item.children.length > 0) {
        getRecursiveSelectedKeys(item.children, selectedKeys, selectedKeysInfo);
      }
    });
  };

  const expandNode = (isLeaf, childrens, id) => {
    if (!isLeaf && childrens < 1) {
      dispatch(setExpandedKeys([...expandedKeys, id]));
    }
  };

  const pathname =
    import.meta.env.VITE_APP_BASE_URL !== "/"
      ? location.pathname.replace(import.meta.env.VITE_APP_BASE_URL, "")
      : location.pathname;

  const handleSelect = (keys: number[], event) => {
    if (!dropdownOpen) {
      if (keys.length === 1) {
        if (
          fromDQM ||
          (parentBreadcrumbs?.length > 0 &&
            parentBreadcrumbs[0].title === "DQM")
        ) {
          if (
            [
              "Test negatywny",
              "Test nieaktywny",
              "Test nie wystartowany",
              "Test pozytywny",
              "Test",
            ].includes(getTemplateName(event?.node?.templateId))
          ) {
            navigate(`/details/${params?.nodeId}?nodeId=${keys[0]}`);
          } else {
            navigate(`/details/${params?.nodeId}?nodeId=${keys[0]}`);
          }
        } else {
          navigate(`${pathname}?nodeId=${keys[0]}`);
        }
        const focusedNodes = { ...focusedNode };
        focusedNodes[params?.nodeId] = keys[0];
        dispatch(setFocusedNode(focusedNodes));
        dispatch(setBreadcrumb([...event.node.breadcrumb]));
      }
      if (keys.length < 2) {
        const selectedKeys = [];
        event?.selectedNodes.forEach((item) => {
          selectedKeys.push({
            id: item.key,
            parentId: item.parentId,
            name: item.name,
            isAsset: item.templateId === 2,
            templateId: item?.templateId,
            body: item?.body,
            isLeaf: item?.isLeaf,
            flag: getFlags(item?.bitFlag),
          });
        });
        dispatch(setSelected({ keys: [...keys], info: [...selectedKeys] }));
      } else {
        const selectedKeysInfo = [];
        const selectedKeys = [];

        getRecursiveSelectedKeys(
          event?.selectedNodes,
          selectedKeys,
          selectedKeysInfo
        );

        dispatch(
          setSelected({ keys: [...selectedKeys], info: [...selectedKeysInfo] })
        );
        if (!tempBreadcrumbs) {
          dispatch(setTempBreadcrumbs(breadcrumb));
        }

        dispatch(
          setBreadcrumb([
            {
              title: "Multiple selected",
              multipleSelect: true,
            },
          ])
        );
      }
    }
  };

  const handleExpand = async (
    _,
    event: {
      node: EventDataNode<ITreeData>;
      expanded: boolean;
      nativeEvent: any;
    }
  ) => {
    const isExpandIconClicked = event.nativeEvent.target.classList.contains(
      "ant-tree-switcher-icon"
    );

    if (!isExpandIconClicked) {
      return;
    }

    const getAllDescendantKeys = (node) => {
      const keys = [];
      const traverse = (n) => {
        keys.push(n.key);
        if (n.children) {
          n.children.forEach(traverse);
        }
      };
      traverse(node);
      return keys;
    };

    let newExpandedKeys = null;
    if (expandedKeys.includes(event.node.key)) {
      // Only update expandedKeys for UI; do NOT remove children from treeData
      const descendantKeys = getAllDescendantKeys(event.node);
      newExpandedKeys = expandedKeys.filter(
        (key) => !descendantKeys.includes(key)
      );
      dispatch(setExpandedKeys([...newExpandedKeys]));
      // Do NOT modify treeData here; keep all children in memory
    } else {
      newExpandedKeys = [...expandedKeys, event.node.key];
      dispatch(setExpandedKeys([...newExpandedKeys]));
      // If children are not loaded, load them as before
      if (
        event.node.loaded &&
        !event.node.isLeaf &&
        event.node.children.length === 0
      ) {
        const data = await getAllNodes(event.node.key.toString());
        const parentNodes = [];
        data?.forEach((value: ITreeData) => {
          parentNodes.push({
            key: value.id,
            parentId: event.node.key,
            name: value.name,
            body: value?.body,
            permissionsId: value?.permissionsId,
            title: value.name,
            icon: getTemplateIcon(value.templateId),
            templateId: value.templateId,
            isLeaf: value?.countChildren === 0,
            visualOrder: value?.visualOrder,
            flag: getFlags(value?.bitFlag),
            children: [],
            allowedChildren:
              value?.templateId == TEMP_GROUPING_NODE_ID
                ? value?.body?.find((body) => body?.type === "allowedChildren")
                    ?.value
                : [],
            breadcrumb: [
              ...breadcrumb,
              {
                id: value?.id,
                title: value?.name,
                parentId: event.node.key,
                templateName: getTemplateName(value.templateId),
                templateId: value.templateId,
                allowedChildrens:
                  value?.templateId == TEMP_GROUPING_NODE_ID
                    ? value?.body?.find(
                        (body) => body?.type === "allowedChildren"
                      )?.value || []
                    : getAllowedChildrens(value.templateId),
              },
            ],
          });
        });
        setTreeData(updateTreeData(treeData, event.node.key, parentNodes));
      }
    }
  };

  useEffect(() => {
    setScrollToNode(true);
  }, [params?.nodeId]);

  useEffect(() => {
    if (moveToFocused) {
      setScrollToNode(true);
      dispatch(setMoveToFocused(false));
    }
  }, [moveToFocused]);

  useEffect(() => {
    setTimeout(() => {
      if (
        scrollToNode &&
        treeRef.current &&
        !isExpanding &&
        selected?.keys?.length > 0
      ) {
        const selectedNode = treeRef.current.querySelector(
          `.ant-tree-treenode-selected`
        );

        if (selectedNode) {
          setScrollToNode(false);
          selectedNode.scrollIntoView({ behavior: "smooth", block: "center" });
        } else if (treeComponentRef.current) {
          treeComponentRef.current.scrollTo({
            key: selected.keys[0],
            align: "top",
          });
          setScrollToNode(false);
        }
      }
    }, 500);
  }, [scrollToNode, isExpanding, selected?.keys]);

  // Memoized allowDrop for performance
  const allowDrop = useCallback(
    ({ dropNode, dragNode, dropPosition }) => {
      const isGapDrop = dropPosition === -1 || dropPosition === 1;
      const info = {
        node: dropNode,
        dragNode: dragNode,
        dropPosition: dropPosition,
        dropToGap: isGapDrop,
      };
      const validation = validateSidebarDrop(
        info,
        treeData,
        templatesData,
        nodeId,
        metamodel,
        queryClient
      );
      setDragPosition((prev) => {
        if (
          !prev ||
          prev.key !== dropNode.key ||
          prev.dropToGap !== isGapDrop ||
          prev.dropPosition !== dropPosition
        ) {
          return { key: dropNode.key, dropToGap: isGapDrop, dropPosition };
        }
        return prev;
      });
      setValidationStatus((prev) => {
        if (
          !prev ||
          prev.key !== dropNode.key ||
          prev.status !== (validation.valid ? "valid" : "invalid")
        ) {
          return {
            key: dropNode.key,
            status: validation.valid ? "valid" : "invalid",
          };
        }
        return prev;
      });
      return true;
    },
    [treeData, templatesData, nodeId, metamodel, queryClient]
  );

  // Memoized titleRender for performance
  const titleRender = useCallback(
    (node) => {
      const isCurrentDropTarget =
        !!dragPosition && dragPosition.key === node.key;
      const currentValidationStatus =
        isCurrentDropTarget && validationStatus?.key === node.key
          ? validationStatus.status
          : null;
      return (
        <SidebarTreeLabel
          setAction={setAction}
          handleSort={handleSort}
          id={node.key}
          label={node.name}
          parentId={node.parentId}
          setDropdownOpen={setDropdownOpen}
          templateId={node.templateId}
          allowedChildren={node.allowedChildrens}
          icon={node.icon}
          breadcrumbs={node.breadcrumb}
          isLeaf={node.isLeaf}
          childrens={node?.children?.length || 0}
          expandNode={expandNode}
          flag={node.flag}
          count={
            !node.isLeaf && node?.children?.length > 0
              ? node.children.length
              : node.countChildren
          }
          isDropTarget={isCurrentDropTarget}
          dropValidationStatus={currentValidationStatus}
          dropPosition={isCurrentDropTarget ? dragPosition.dropPosition : null}
        />
      );
    },
    [
      dragPosition,
      validationStatus,
      setAction,
      handleSort,
      setDropdownOpen,
      expandNode,
    ]
  );

  return (
    <div
      className="tree tree-container"
      ref={(el) => {
        if (el) {
          treeRef.current = el;
          containerRef.current = el;
        }
      }}
    >
      {contextHolder}
      <Tree.DirectoryTree
        ref={treeComponentRef}
        showLine
        showIcon={false}
        draggable={!disabled}
        multiple
        blockNode
        virtual
        height={containerHeight}
        onClick={(e) => e.stopPropagation()}
        className="hide-draggable-icon"
        autoExpandParent
        onSelect={handleSelect}
        switcherIcon={(val) => {
          return (
            <DownIcon
              style={{
                transform: val.expanded ? `rotate(0deg)` : "rotate(-90deg)",
              }}
            />
          );
        }}
        allowDrop={allowDrop}
        onDragLeave={(info) => {
          if (
            treeRef.current &&
            !treeRef.current.contains(info.event.relatedTarget as Node)
          ) {
            setDragPosition(null);
            setValidationStatus(null);
          }
        }}
        titleRender={titleRender}
        expandedKeys={expandedKeys}
        treeData={treeData}
        selectedKeys={(selected?.keys as any) || []}
        onDragStart={(e) => {
          const permissions = getPermissions(e.node?.permissionsId);
          if (!permissions.includes("MOVE")) {
            e.event.preventDefault();
            return;
          }

          // Store the original tree data when drag starts
          // This is critical for the visual order optimization
          if (!movingMask) {
            setTempTreeData(deepClone(treeData));
          }

          if (selected.keys.length >= 2) {
            showInfoNotification(
              "Feature Limitation: Dragging multiple items is not supported.",
              "Opps!"
            );
          }
        }}
        onDrop={async (e) => {
          if (selected.keys.length <= 1) {
            // We already stored the original tree data in onDragStart
            // Now we just need to handle the drag operation
            setTimeout(() => {
              handleSidebarDrag(
                e,
                treeData,
                setTreeData,
                tempTreeData,
                setTempTreeData
              );
            }, 300);
          }
          // Reset drag indicator after drop
          setDragPosition(null);
          setValidationStatus(null); // Also reset validation status
        }}
        onDragEnd={() => {
          setDragPosition(null);
          setValidationStatus(null);
        }}
        loadData={async (treeNode) => {
          if (expandedKeys.includes(treeNode.key)) {
            return onLoadData(treeNode);
          }
          return Promise.reject();
        }}
        onExpand={dropdownOpen ? () => null : handleExpand}
      />
    </div>
  );
};

export { SidebarTree };
