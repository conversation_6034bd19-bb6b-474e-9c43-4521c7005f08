import { styled } from "@linaria/react";
import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";

const Iframe = ({ attributeValue, onEditClick, readOnly = false }) => {
  const { t } = useTranslation();

  const generateURL = () => {
    if (!attributeValue?.url.startsWith("http")) {
      return `https://${attributeValue.url}`;
    }

    return attributeValue?.url;
  };

  return (
    <Container
      style={{
        width: attributeValue?.width || 600,
        height: attributeValue?.height || 400,
      }}
      className={readOnly ? "read-only" : ""}
    >
      {!readOnly && (
        <div className="edit" onClick={onEditClick}>
          <Tooltip title={t("Edit")}>
            <i className="pi pi-pencil" />
          </Tooltip>
        </div>
      )}
      <iframe
        sandbox="allow-scripts allow-same-origin"
        width={attributeValue?.width || "100%"}
        height={attributeValue?.height || "100%"}
        src={generateURL()}
        title={"Error in displaying iframe"}
      />
    </Container>
  );
};

export { Iframe };

const Container = styled.div`
  position: relative;

  &.read-only {
    pointer-events: none;
  }

  & .edit {
    position: absolute;
    transition: all 0.5s;
    right: 10px;
    background-color: #eeeeee;
    top: 10px;
    padding: 4px 7px;
    border-radius: 50%;
    display: none;
  }

  &:hover .edit {
    display: block;
    transition: all 0.5s;
  }
`;
