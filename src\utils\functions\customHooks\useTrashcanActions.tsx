import { useQueryClient } from "react-query";
import { RootState } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import { ITreeData } from "../../../interfaces";
import { useNavigate, useParams } from "react-router-dom";
import { GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID } from "../../../constants";
import { setSelected } from "../../../store/features/sidebar";
import { useTemplateActions } from "./useTemplateActions";
import { setBreadcrumb } from "../../../store/features";
import {
  setExpandedTrashKeys,
  setPerformTrash,
  setSelectedTrash,
} from "../../../store/features/trashcan";

const pathname =
  import.meta.env.VITE_APP_BASE_URL !== "/"
    ? location.pathname.replace(import.meta.env.VITE_APP_BASE_URL, "")
    : location.pathname;

const useTrashcanActions = () => {
  const { selectedTrash, expandedTrashKeys } = useSelector(
    (state: RootState) => state.trash
  );

  const { getTemplateIcon } = useTemplateActions();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const params = useParams();
  const parentMenuNodeId = params?.nodeId;

  // function to delete multiple nodes
  const handleTrashNodeDelete = (treeData) => {
    deleteNode(treeData, selectedTrash.keys);

    // console.log(treeData)
    if (treeData.length > 0) {
      dispatch(
        setSelectedTrash({
          keys: [treeData[0].key],
          info: [
            {
              id: treeData[0].key,
              parentId: treeData[0].parentId,
              name: treeData[0].name,
              isLeaf: treeData[0].isLeaf,
              templateId: treeData[0].templateId,
            },
          ],
        })
      );
    }

    let allExpanded = [...expandedTrashKeys];
    selectedTrash.info?.forEach((item) => {
      allExpanded = allExpanded.filter((expanded) => expanded !== item.id);
    });

    dispatch(setPerformTrash(null));

    dispatch(setExpandedTrashKeys([...allExpanded]));

    if (parentMenuNodeId) {
      const previousData = queryClient.getQueryData([
        GET_CHILDRENS,
        parentMenuNodeId.toString(),
      ]) as any[];

      const updatedQueryCacheData = handleRecursiveQueryClientDelete(
        [...previousData],
        selectedTrash.keys
      );

      queryClient.setQueryData(
        [GET_CHILDRENS, parentMenuNodeId.toString()],
        updatedQueryCacheData
      );
    }
  };

  // const deletePinned = (deleteIds: number[]) => {
  //   let newFavorites = [...pinned];

  //   deleteIds.forEach((id) => {
  //     newFavorites = newFavorites?.filter((fav) => fav?.id !== id);
  //     pinnedMutation.mutateAsync({
  //       value: {
  //         ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
  //         pinned: newFavorites,
  //       },
  //     });
  //     dispatch(setPinned([...newFavorites]));
  //   });
  // };

  const generateSearchTrashDataRecursively = (data: ITreeData[]) => {
    return (
      data?.map((node: ITreeData) => {
        return {
          key: node?.id,
          templateId: node.templateId,
          name: node.name,
          nodeType: node.nodeType,
          title: node.name,
          icon: getTemplateIcon(node.templateId),
          parentId: node.parentId,
          isLeaf: node?.countChildren === 0,
          children:
            (node?.children?.length > 0 &&
              generateSearchTrashDataRecursively(node?.children)) ||
            [],
        };
      }) || []
    );
  };

  const generateTrashDataRecursively = (data: ITreeData[]) => {
    return (
      data?.map((node: ITreeData) => {
        //  allowedChildrens: getAllowedChildrens(node.templateId),

        const childrenCache = queryClient.getQueryData([
          GET_CHILDRENS,
          node?.id?.toString(),
        ]) as ITreeData[];

        return {
          key: node?.id,
          templateId: node.templateId,
          name: node.name,
          nodeType: node.nodeType,
          title: node.name,
          icon: getTemplateIcon(node.templateId),
          parentId: node.parentId,
          isLeaf: node?.countChildren === 0,
          children:
            (childrenCache?.length > 0 &&
              generateTrashDataRecursively(childrenCache)) ||
            [],
        };
      }) || []
    );
  };

  const handleRecursiveQueryClientDelete = (
    data,
    deleteKeys,
    parent = null
  ) => {
    const newData = data.filter((item) => !deleteKeys.includes(item.id));
    if (newData.length === 0 && parent) {
      parent.last = true;
    }

    newData.forEach((item) => {
      if (item.children && item.children.length > 0) {
        handleRecursiveQueryClientDelete(item.children, deleteKeys, item);
      }
    });

    return newData;
  };

  const updateNewIdRecursively = (data, oldRandomId, newId) => {
    const BreakException = {};
    try {
      data.forEach((item) => {
        if (item.key == oldRandomId) {
          const breadcrumbs = item.breadcrumb.map((crumb, index) => {
            // Create a shallow copy of each object in the array
            return index === item.breadcrumb.length - 1
              ? { ...crumb, id: newId } // Modify 'id' for the last object
              : { ...crumb }; // Keep other objects unchanged
          });
          item.breadcrumb = breadcrumbs;
          item.key = newId;

          // const displayedBreadcrumb = breadcrumb.map(({ ...data }) => data);
          // const index = displayedBreadcrumb.findIndex(
          //   (data) => data.id === item.key
          // );

          // if (index !== -1) {
          //   displayedBreadcrumb[index].id = newId;
          //   dispatch(setBreadcrumb(displayedBreadcrumb));
          // }
          throw BreakException;
        } else if (item.children && item.children.length > 0) {
          updateNewIdRecursively(item.children, oldRandomId, newId);
        }
      });
    } catch (e) {
      if (e !== BreakException) throw e;
    }
  };

  const deleteNodesRecursively = (
    data: ITreeData[],
    target: number,
    parent?: any
  ) => {
    const index = data?.findIndex((item) => item?.key == target);
    if (index !== -1) {
      data.splice(index, 1);
      if (data.length === 0 && parent) {
        parent.isLeaf = true;
      }
      return data;
    }
    data.forEach((item) => {
      if (item.children && item.children.length > 0) {
        deleteNodesRecursively(item.children, target, item);
      }
    });
    return data;
  };

  const deleteNode = (treeData, selectedKeys) => {
    selectedKeys?.forEach((toDeleteId) => {
      deleteNodesRecursively(treeData, toDeleteId);
    });
  };

  // function to delete multiple nodes
  const handleNodeDelete = (treeData) => {
    deleteNode(treeData, selectedTrash.keys);

    // console.log(treeData)
    if (treeData.length > 0) {
      navigate(`${pathname}?nodeId=${treeData[0].key}`);
      dispatch(
        setSelected({
          keys: [treeData[0].key],
          info: [
            {
              id: treeData[0].key,
              parentId: treeData[0].parentId,
              name: treeData[0].name,
              isLeaf: treeData[0].isLeaf,
            },
          ],
        })
      );
      dispatch(setBreadcrumb(treeData[0].breadcrumb));
    } else {
      navigate(`${pathname}`);
      dispatch(setBreadcrumb([]));
    }

    let allExpanded = [...expandedTrashKeys];
    selectedTrash.info?.forEach((item) => {
      allExpanded = allExpanded.filter((expanded) => expanded !== item.id);
    });
    dispatch(setExpandedTrashKeys([...allExpanded]));

    const previousData = queryClient.getQueryData([
      GET_CHILDRENS,
      parentMenuNodeId.toString(),
    ]) as any[];

    const updatedQueryCacheData = handleRecursiveQueryClientDelete(
      [...previousData],
      selectedTrash.keys
    );

    queryClient.setQueryData(
      [GET_CHILDRENS, parentMenuNodeId.toString()],
      updatedQueryCacheData
    );
  };

  // function to restore multiple nodes
  const handleNodeRestore = (treeData) => {
    deleteNode(treeData, selectedTrash.keys);

    if (treeData.length > 0) {
      dispatch(
        setSelectedTrash({
          keys: [treeData[0].key],
          info: [
            {
              id: treeData[0].key,
              parentId: treeData[0].parentId,
              name: treeData[0].name,
              isLeaf: treeData[0].isLeaf,
              templateId: treeData[0].templateId,
            },
          ],
        })
      );
    }

    let allExpanded = [...expandedTrashKeys];
    selectedTrash.info?.forEach((item) => {
      allExpanded = allExpanded.filter((expanded) => expanded !== item.id);
    });
    dispatch(setExpandedTrashKeys([...allExpanded]));

    const previousData = queryClient.getQueryData([
      GET_CHILDRENS,
      TRASHCAN_PARENT_NODE_ID,
    ]) as any[];

    const updatedQueryCacheData = handleRecursiveQueryClientDelete(
      [...previousData],
      selectedTrash.keys
    );

    queryClient.setQueryData(
      [GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID],
      updatedQueryCacheData
    );
  };

  return {
    generateTrashDataRecursively,
    handleNodeDelete,
    updateNewIdRecursively,
    handleNodeRestore,
    handleTrashNodeDelete,
    generateSearchTrashDataRecursively,
  };
};

export { useTrashcanActions };
