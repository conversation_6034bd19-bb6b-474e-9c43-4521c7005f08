import { styled } from "@linaria/react";
import { Modal, Radio, Tooltip, Tree } from "antd";
import { ReactComponent as DownIcon } from "../../../../assets/mdi_caret.svg";
import { ReactComponent as FolderIcon } from "../../../../assets/folder.svg";
import { ReactComponent as FolderOpenIcon } from "../../../../assets/folderOpen.svg";
import { ReactComponent as SearchIcon } from "../../../../assets/search.svg";
interface Props {
  onClose: () => void;
  isOpen: boolean;
}

interface ITitle {
  title: string;
  description: string;
}

const TreeTitleNode = ({ title, description }: ITitle) => (
  <TreeTitle>
    <p className="title">{title}</p>
    <p className="info">
      <Tooltip title="Details" placement="left">
        <SearchIcon />
        {description}
      </Tooltip>
    </p>
  </TreeTitle>
);

const ExportModal = ({ isOpen, onClose }: Props) => {
  const treeData = [
    {
      title: <TreeTitleNode title="Systemy" description=" Grupa systemów" />,
      key: "0-0",
      icon: ({ expanded }) => (expanded ? <FolderOpenIcon /> : <FolderIcon />),
      children: [
        {
          icon: ({ expanded }) =>
            expanded ? <FolderOpenIcon /> : <FolderIcon />,
          title: (
            <TreeTitleNode
              title="Serwisy webowe"
              description=" Grupa systemów"
            />
          ),
          key: "0-0-0",
          children: [
            {
              title: (
                <TreeTitleNode
                  title="Strona www"
                  description="System informatyczny"
                />
              ),

              key: "0-0-0-0",
            },
            {
              title: (
                <TreeTitleNode
                  title="Super WEB Księga"
                  description="System informatyczny"
                />
              ),
              key: "0-0-0-1",
            },
          ],
        },
        {
          icon: ({ expanded }) =>
            expanded ? <FolderOpenIcon /> : <FolderIcon />,
          title: (
            <TreeTitleNode
              title="Systemy informatyczne"
              description=" Grupa systemów"
            />
          ),
          key: "0-0-1",
          children: [
            {
              title: (
                <TreeTitleNode title="CRM" description="System informatyczny" />
              ),
              key: "0-0-1-0",
            },
            {
              title: (
                <TreeTitleNode
                  title="Freshmail"
                  description="System informatyczny"
                />
              ),
              key: "0-0-1-1",
            },
          ],
        },
      ],
    },
  ];

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      cancelText={"Close"}
      okText={"Export"}
      centered
      className="export-modal"
      title={"Export"}
      width="80%"
    >
      <RadioWrapper>
        <Radio.Group>
          <Radio value="zip">Tree export (.zip)</Radio>
          <Radio value="html">Create a document (.html)</Radio>
          <Radio value="pdf">Create a pdf (.pdf)</Radio>
        </Radio.Group>
      </RadioWrapper>
      <TreeWrapper>
        <Tree
          blockNode
          defaultExpandedKeys={["0-0"]}
          checkable
          treeData={treeData}
          showIcon
          showLine
          switcherIcon={(val) => {
            return (
              <DownIcon
                style={{
                  transform: val.expanded ? `rotate(0deg)` : "rotate(-90deg)",
                }}
              />
            );
          }}
        />
      </TreeWrapper>
    </Modal>
  );
};

export { ExportModal };

const TreeTitle = styled.div`
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-right: 50px;

  & .info {
    font-size: 12px;
    min-width: 160px;
    & > span {
      display: flex;
      gap: 3px;

      & svg {
        width: 17px;
        height: 17px;
        margin-top: 3px;
        fill: #cdcdcd;
      }
    }
  }
`;
const TreeWrapper = styled.div`
  padding-top: 10px;

  & .ant-tree-checkbox {
    margin-top: 2px;
    margin-right: 5px;
  }
  & .ant-tree-title {
    color: #084f8a;
    width: 100%;
  }
  & .ant-tree-iconEle {
    width: 16px !important;
    height: 16px !important;
    line-height: unset !important;
    margin-top: 1px;
    margin-right: 6px;
    & svg {
      width: 16px;
      height: 16px;
    }
  }

  & .ant-tree-switcher svg {
    width: 16px;
    height: 16px;
    transition: all 0.4s;
  }
`;

const RadioWrapper = styled.div`
  border-bottom: 1px solid #084f8a30;
  padding: 11px 0;

  & label {
    font-size: 13px;
    color: #084f8a;
  }
`;
