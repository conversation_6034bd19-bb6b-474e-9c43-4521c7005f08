import { styled } from "@linaria/react";
import { Button, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import {
  deleteMultipleNodeService,
  deleteNodeService,
} from "../../../../services/node";
import { useEffect, useRef } from "react";
import {
  GET_CHILDRENS,
  TRASHCAN_PARENT_NODE_ID,
  getAttributeIcon,
} from "../../../../constants";
import { captureException } from "@sentry/react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { Dialog } from "primereact/dialog";

const DeleteAttributeTemplateModal = ({
  onClose,
  isOpen,
  id,
  label,
  onDeleteClick,
  afterDelete,
  isMultiple,
}: Props) => {
  const { t } = useTranslation();
  const ref = useRef<any>();
  const { selected } = useSelector((state: RootState) => state.sidebar);
  const queryClient = useQueryClient();
  const templateIcon = getAttributeIcon("_30_attribute");

  useEffect(() => {
    ref?.current?.focus();
  }, [isOpen]);

  const mutation = useMutation(
    isMultiple ? deleteMultipleNodeService : deleteNodeService,
    {
      onSuccess: () => {
        afterDelete && afterDelete();
        queryClient.invalidateQueries([GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID]);
        const toInvalidateParents = [];
        selected?.info?.forEach((item) => {
          toInvalidateParents.push(item.parentId);
        });

        toInvalidateParents.forEach((parentId) => {
          queryClient.invalidateQueries([GET_CHILDRENS, parentId.toString()]);
        });

        notification.success({
          message: t("Success!"),
          description: t("Attribute template deleted successfully!"),
        });

        onClose();
      },
      onError: (e: any) => {
        notification.error({
          message: t("Error Occurred!"),
          description: e?.data?.error || "Please try again after sometime",
        });
        captureException(e?.data?.error);
      },
    }
  );

  const handleDelete = () => {
    if (onDeleteClick) {
      onDeleteClick();
      onClose();
    } else {
      mutation.mutate(isMultiple ? { keys: selected.keys } : { id: id });
    }
  };

  const getModalTitle = () => {
    if (isMultiple) {
      return t("Delete selected");
    }
    return `${t("Delete")} ${label}?`;
  };

  const getModelBody = () => {
    if (isMultiple) {
      return t(
        "Are you sure you want to delete selected attributes templates?"
      );
    }
    return `${t(
      "Are you sure to delete this attribute template permanently?"
    )}`;
  };

  const getButtonLabel = () => {
    if (isMultiple) {
      return t("Delete selected");
    }
    return `${t("Delete")}`;
  };

  return (
    <Dialog
      visible={isOpen}
      onHide={onClose}
      footer={null}
      className="export-modal draggable-modal"
      header={getModalTitle()}
    >
      <Wrapper>
        <h5>{getModelBody()}</h5>
        {!isMultiple && (
          <p>
            {templateIcon} {label}
          </p>
        )}
        <Button
          onClick={handleDelete}
          type="primary"
          ref={ref}
          loading={mutation.isLoading}
        >
          {getButtonLabel()}
        </Button>
      </Wrapper>
    </Dialog>
  );
};

export { DeleteAttributeTemplateModal };

interface Props {
  onClose: () => void;
  isOpen: boolean;
  id: string;
  label: string;
  onDeleteClick?: () => void;
  afterDelete?: () => void;
  isMultiple?: boolean;
  selectedKeys?: number[];
  action?: any;
}

const Wrapper = styled.form`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;

  & img {
    object-fit: contain;
  }

  & h5 {
    font-size: 13px;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 6px;

    & span {
      color: #949494;
      font-size: 12px;
      font-style: italic;
    }
  }

  & p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4277a2;

    & svg {
      width: 20px;
      height: 20px;
    }
  }
  & button {
    margin-left: auto;
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }
`;
