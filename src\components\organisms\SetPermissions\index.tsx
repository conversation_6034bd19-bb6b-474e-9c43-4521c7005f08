import { styled } from "@linaria/react";
import { Button, Empty, notification, Select, Tabs, Tooltip } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { API } from "../../../utils/api";
import { DetailsContainer } from "../DetailsContainer";
import { Hyperlink } from "../../atoms/AttributeItem/Hyperlink";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { useMutation, useQueryClient } from "react-query";
import { clearPermissions, savePermissions } from "../../../services";
import {
  useNotification,
  usePermissions,
} from "../../../utils/functions/customHooks";
import { useSearchParams } from "react-router-dom";
import {
  GET_ALL_PERMISSIONS,
  GET_COUNTERS,
  GET_HISTORY_DATA,
  GET_NODE_ATTRIBUTES_DETAILS,
  GROUP_AD_ID,
  PERMISSION_PERSON_ID,
  PERMISSION_ROLE_ID,
  PERMISSIONS_NODE_ID,
  PERSON_AD_ID,
  USER_GROUP_ID,
} from "../../../constants";
import { INodeDetails } from "../../../interfaces";
import { getNodeDetails } from "../../../services/node";
import { TabsProps } from "antd/lib";
import { SelectGroupTable } from "./SelectGroupTable";
import { SelectUserTable } from "./SelectUserTable";
import { setTrashcanDrawerMask } from "../../../store/features/trashcan";
import { setBottomDrawerMask } from "../../../store/features";

const PERMISSION_ATTRIBUTE = {
  id: -25,
  type: "compound",
  name: "Rights",
  compoundSourceType: {
    COMPOUND_TWO_INDEPENDENT_LISTS: "Lists",
  },
  multiplicityList1: "0..n",
  multiplicityList2: "0..n",
  nameList1: "R",
  nameList2: "O",
  parent1: PERMISSION_ROLE_ID,
  parent2: PERMISSION_PERSON_ID,
  visualization: {
    simple: "Simple view",
  },
  defaultValue: {
    id: 0,
    type: "compound",
  },
  mandatory: false,
};

interface Props {
  initialEditIndex?: string;
  id?: string;
  displaySaveCancel: boolean;
  fromTrashcan: boolean;
}

const SetPermissions = ({
  initialEditIndex,
  id,
  fromTrashcan,
  displaySaveCancel,
}: Props) => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [listData1, setListData1] = useState([]);
  const [editingIndex, setEditingIndex] = useState(null);
  const [selectedList, setSelectedlist] = useState([]);
  const [temp, setTemp] = useState(null);
  const [permissions, setPermissions] = useState([]);

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const { getPermissions } = usePermissions();
  const { contextHolder, showErrorNotification } = useNotification();

  useEffect(() => {
    generateIndependentListData();
  }, []);

  const generateIndependentListData = async () => {
    const params = { addBody: true };
    params["templateIds"] = [PERMISSION_ROLE_ID];

    const response1 = (await API.post(`/model/node/get`, params)) as any;
    const list1 = [];
    response1?.forEach((item) => {
      list1.push({
        label: item.name,
        value: item.id,
        key: item.id,
        id: item.id,
        pathName: item?.pathName,
        templateId: item?.templateId,
        templateHasAttributes: item?.body?.length > 0,
        permissionsId: item?.permissionsId,
      });
    });

    setListData1(list1);
  };

  const loadInitialData = async () => {
    let bodyData = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      id || searchParams.get("nodeId"),
    ]) as INodeDetails;
    if (!bodyData) {
      bodyData = await getNodeDetails(id || searchParams.get("nodeId"));
    }
    if (bodyData) {
      const permissionsData = bodyData?.body?.find(
        (attr) => attr.id === PERMISSIONS_NODE_ID
      );
      setPermissions(getPermissions(bodyData?.permissionsId));
      setSelectedlist(permissionsData?.value || []);
      if (initialEditIndex) {
        const editIndex = permissionsData?.value?.findIndex(
          (val) => val.id === initialEditIndex
        );
        setEditingIndex(editIndex);
        setTemp(permissionsData?.value[editIndex]);
      }
    }
  };

  useEffect(() => {
    loadInitialData();
  }, [searchParams.get("nodeId"), id, initialEditIndex]);

  const validateMultiplicityList1 = () => {
    let disabled = false;
    const multiplicityList1 =
      PERMISSION_ATTRIBUTE?.multiplicityList1?.split("..");
    const max = multiplicityList1[1];

    if (max === "n") {
      //
    } else if (selectedList.length >= Number(max)) {
      disabled = true;
    }
    return disabled;
  };

  const validateMultiplicity = () => {
    let disabled = false;

    if (!temp?.name) {
      disabled = true;
    }
    // if (temp?.value?.length === 0) {
    //   disabled = true;
    // }

    return disabled;
  };

  const clearMutation = useMutation(clearPermissions, {
    onSuccess: () => {
      notification.success({
        message: t("Success!"),
        description: t("Permissions cleared successfully!"),
      });
      queryClient.invalidateQueries(GET_ALL_PERMISSIONS);
      queryClient.invalidateQueries([
        GET_COUNTERS,
        id || searchParams.get("nodeId"),
      ]);
      queryClient.invalidateQueries([
        GET_NODE_ATTRIBUTES_DETAILS,
        id || searchParams.get("nodeId"),
      ]);
      queryClient.invalidateQueries([
        GET_HISTORY_DATA,
        id || searchParams.get("nodeId"),
      ]);
    },
    onError: () => {
      showErrorNotification("Error occurred!");
    },
  });

  const mutation = useMutation(savePermissions, {
    onSuccess: () => {
      notification.success({
        message: t("Success!"),
        description: t("Permissions set successfully!"),
      });
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
      queryClient.invalidateQueries(GET_ALL_PERMISSIONS);
      queryClient.invalidateQueries([
        GET_COUNTERS,
        id || searchParams.get("nodeId"),
      ]);
      queryClient.invalidateQueries([
        GET_NODE_ATTRIBUTES_DETAILS,
        id || searchParams.get("nodeId"),
      ]);
      queryClient.invalidateQueries([
        GET_HISTORY_DATA,
        id || searchParams.get("nodeId"),
      ]);
    },
    onError: () => {
      showErrorNotification("Error occurred!");
    },
  });

  const handleSave = () => {
    if (selectedList?.length === 0) {
      clearMutation.mutate(id || searchParams.get("nodeId"));
    } else {
      const payload = {};
      selectedList?.forEach((list) => {
        payload[list.id] = list?.value?.map((list2) => list2.id);
      });
      mutation.mutate({
        nodeId: id || searchParams.get("nodeId"),
        value: payload,
      });
    }
  };

  const tabItems: TabsProps["items"] = [
    {
      key: "person",
      label: "Osoby",
      children: (
        <SelectUserTable
          defaultSelected={temp?.value?.filter(
            (item) =>
              item.templateId === PERMISSION_PERSON_ID ||
              item.templateId === PERSON_AD_ID
          )}
          onSelect={(selected) => {
            const newSelected = { ...temp };

            const combined = [...newSelected.value, ...selected];
            const uniqueById = Array.from(
              new Map(combined.map((item) => [item.id, item])).values()
            );
            newSelected.value = uniqueById;
            setTemp(newSelected);
          }}
        />
      ),
    },
    {
      key: "group",
      label: "Grupy",
      children: (
        <SelectGroupTable
          defaultSelected={temp?.value?.filter(
            (item) =>
              item.templateId === GROUP_AD_ID ||
              item.templateId === USER_GROUP_ID
          )}
          onSelect={(selected) => {
            const newSelected = { ...temp };

            const combined = [...newSelected.value, ...selected];
            const uniqueById = Array.from(
              new Map(combined.map((item) => [item.id, item])).values()
            );
            newSelected.value = uniqueById;
            setTemp(newSelected);
          }}
        />
      ),
    },
  ];

  const detectChange = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(true));
    } else {
      dispatch(setBottomDrawerMask(true));
    }
  };

  const handleCancel = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(false));
    } else {
      dispatch(setBottomDrawerMask(false));
    }
    loadInitialData();
  };

  return (
    <Wrapper>
      {displaySaveCancel && (
        <div className="buttons">
          <Button
            className="primary-button cancel-button"
            onClick={handleCancel}
          >
            {t("Cancel")}
          </Button>
          <Button
            className="primary-button save-button"
            onClick={handleSave}
            loading={mutation.isLoading}
          >
            {t("Save")}
          </Button>
        </div>
      )}

      {contextHolder}
      <div className="content">
        <Flex>
          <div className="list">
            <h6>{templatesData[PERMISSION_ROLE_ID]?.name}</h6>
          </div>
          <div className="list">
            <h6>
              {templatesData[PERMISSION_PERSON_ID]?.name} (
              {PERMISSION_ATTRIBUTE?.multiplicityList2})
            </h6>
          </div>

          {permissions?.includes("PERMISSION") && (
            <div className="actions">
              {selectedList.length < listData1.length &&
                (!temp || temp?.id) &&
                !temp?.isNew &&
                !validateMultiplicityList1() && (
                  <Tooltip title={t("Add New")}>
                    <Button
                      type="primary"
                      className="flex-1 add-new"
                      icon={<i className="pi pi-plus" />}
                      onClick={() => {
                        setTemp({
                          isNew: true,
                          id: null,
                          name: null,
                          value: [],
                          templateHasAttributes: false,
                          permissionsId: 0,
                        });

                        setEditingIndex(selectedList.length);
                      }}
                    />
                  </Tooltip>
                )}
            </div>
          )}
        </Flex>
        {selectedList.length === 0 && !temp ? (
          <Flex>
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={false} />
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={false} />
            <div className="actions" />
          </Flex>
        ) : (
          selectedList?.map((selected, index) => (
            <Flex
              key={`${selected.id}-${index}`}
              style={{ borderBottom: "1px solid #eee" }}
            >
              {editingIndex === index ? (
                <>
                  <Select
                    defaultOpen
                    autoFocus
                    allowClear
                    disabled={!!selected.id}
                    options={listData1?.map((item) => ({
                      ...item,
                      title: null,
                      disabled: selectedList?.some(
                        (selected) => selected.id === item.value
                      ),
                    }))}
                    value={temp.id}
                    onChange={(_, item: any) => {
                      const newSelected = { ...temp };
                      newSelected.id = item.value;
                      newSelected.name = item.label;
                      newSelected.templateHasAttributes =
                        item.templateHasAttributes;
                      newSelected.permissionsId = item.permissionsId;
                      newSelected.pathName = item?.pathName;
                      newSelected.templateId = item?.templateId;

                      setTemp(newSelected);
                    }}
                    showSearch
                    filterOption={(input, option) => {
                      return (option?.label?.toLowerCase() ?? "").includes(
                        input.toLowerCase()
                      );
                    }}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? "")
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toLowerCase())
                    }
                  />

                  <TabsContainer>
                    <Tabs defaultActiveKey="person" items={tabItems} />
                  </TabsContainer>
                </>
              ) : (
                <>
                  <div
                    className="hyperlink"
                    // onClick={() => {
                    //   setEditingIndex(index);
                    //   setTemp(selected);
                    // }}
                  >
                    {selected.id && <Hyperlink val={[selected]} />}
                  </div>
                  <div
                    className="hyperlink"
                    // onClick={() => {
                    //   setEditingIndex(index);
                    //   setTemp(selected);
                    // }}
                  >
                    {selected.value?.length > 0 ? (
                      <Hyperlink val={selected.value} />
                    ) : (
                      <p className="no-data">—</p>
                    )}
                  </div>
                </>
              )}

              {permissions?.includes("PERMISSION") && (
                <div className="actions">
                  {editingIndex === index ? (
                    <>
                      <Tooltip
                        title={
                          !temp?.name
                            ? t(
                                `${templatesData[PERMISSION_ROLE_ID]?.name} is required`
                              )
                            : temp?.value?.length === 0
                            ? t(
                                `${templatesData[PERMISSION_PERSON_ID]?.name} is required`
                              )
                            : t("Save")
                        }
                        placement="leftBottom"
                      >
                        <Button
                          type="primary"
                          icon={<i className="pi pi-save" />}
                          className="save-btn"
                          onClick={() => {
                            const newSelected = [...selectedList];
                            newSelected[index] = temp;
                            setSelectedlist([...newSelected]);
                            setEditingIndex(null);
                            setTemp(null);
                            detectChange();
                          }}
                          disabled={validateMultiplicity()}
                        />
                      </Tooltip>

                      <Tooltip title={t("Cancel")} placement="leftBottom">
                        <Button
                          type="primary"
                          className="cancel-btn"
                          icon={<i className="pi pi-ban" />}
                          onClick={() => {
                            if (temp.isNew) {
                              const newSelected = [...selectedList];
                              newSelected.splice(index, 1);
                              setSelectedlist([...newSelected]);
                            }
                            setTemp(null);
                            setEditingIndex(null);
                          }}
                        />
                      </Tooltip>
                    </>
                  ) : (
                    <>
                      <Tooltip title={t("Edit")} placement="leftBottom">
                        <Button
                          type="primary"
                          className="edit-button"
                          icon={<EditOutlined />}
                          onClick={() => {
                            setEditingIndex(index);
                            setTemp(selected);
                          }}
                        />
                      </Tooltip>

                      <Tooltip title={t("Delete")} placement="leftBottom">
                        <Button
                          type="primary"
                          className="delete-button"
                          icon={<DeleteOutlined />}
                          onClick={() => {
                            const newSelected = [...selectedList];
                            newSelected.splice(index, 1);
                            setSelectedlist([...newSelected]);
                            setEditingIndex(null);
                            setTemp(null);
                          }}
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
              )}
            </Flex>
          ))
        )}

        {temp?.isNew && (
          <Flex>
            <>
              <Select
                defaultOpen
                autoFocus
                allowClear
                options={listData1?.map((item) => ({
                  ...item,
                  title: null,
                  disabled: selectedList?.some(
                    (selected) => selected.id === item.value
                  ),
                }))}
                value={temp.id}
                onChange={(_, item: any) => {
                  const newSelected = { ...temp };
                  newSelected.id = item.value;
                  newSelected.pathName = item?.pathName;
                  newSelected.templateId = item?.templateId;
                  newSelected.name = item.label;
                  newSelected.templateHasAttributes =
                    item.templateHasAttributes;
                  newSelected.permissionsId = item?.permissionsId;

                  setTemp(newSelected);
                }}
                showSearch
                filterOption={(input, option) => {
                  return (option?.label?.toLowerCase() ?? "").includes(
                    input.toLowerCase()
                  );
                }}
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
              />

              <TabsContainer>
                <Tabs defaultActiveKey="person" items={tabItems} />
              </TabsContainer>
            </>

            <div className="actions">
              <Tooltip
                title={
                  !temp?.name
                    ? t(
                        `${templatesData[PERMISSION_ROLE_ID]?.name} is required`
                      )
                    : temp?.value?.length === 0
                    ? t(
                        `${templatesData[PERMISSION_PERSON_ID]?.name} is required`
                      )
                    : t("Save")
                }
              >
                <Button
                  type="primary"
                  icon={<i className="pi pi-save" />}
                  className="save-btn"
                  disabled={validateMultiplicity()}
                  onClick={() => {
                    const newSelected = [...selectedList];
                    const newTemp = { ...temp };
                    delete newTemp["isNew"];
                    newSelected.push(newTemp);
                    setSelectedlist([...newSelected]);
                    setEditingIndex(null);
                    setTemp(null);
                    detectChange();
                  }}
                />
              </Tooltip>

              <Tooltip title={t("Cancel")}>
                <Button
                  type="primary"
                  className="cancel-btn"
                  icon={<i className="pi pi-ban" />}
                  onClick={() => {
                    if (temp.isNew) {
                      setEditingIndex(null);
                      setTemp(null);
                    }
                    setTemp(null);
                    setEditingIndex(null);
                  }}
                />
              </Tooltip>
            </div>
          </Flex>
        )}
      </div>
      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Wrapper>
  );
};

export { SetPermissions };

const TabsContainer = styled.div`
  & .ant-tabs-tab-btn {
    font-size: 13px;
    color: var(--color-text);
  }
`;

const Wrapper = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;

  & .ant-tabs {
    margin-left: 6px;
    gap: 10px;
  }

  & .ant-tabs-nav {
    padding-left: 0px;
  }

  & .save-btn {
    background-color: white;
    color: green;
    border: none;
    & i {
      font-size: 14px;
    }
    &:disabled {
      color: #00800094 !important;
    }
    &:hover {
      background-color: #fff !important;
      opacity: 0.8;
      color: green !important;
    }
  }

  & .content {
    /* overflow: auto; */
    padding-right: 14px;
  }

  & td {
    border: none;
  }

  & .title-container {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  & .edit-button {
    background-color: white;
    color: var(--color-text);
    width: fit-content !important;
    margin: auto;
    &:hover {
      background: white !important;
      color: var(--color-text) !important;
    }
  }

  & .delete-button {
    background-color: white !important;
    color: red;
    width: fit-content !important;
    margin: auto;
    &:hover {
      background: white !important;
      color: red !important;
    }
  }

  & h6 {
    font-size: 13px;
    cursor: pointer;
    font-weight: 400;
    align-items: center;
    background-color: var(--color-light);
    border-right: 1px solid #eaeaea;
    color: var(--color-text);
    padding: 6px;
    display: flex;
    gap: 6px;
    max-width: 80%;
  }

  & .buttons {
    display: flex;
    justify-content: right;
    gap: 10px;
    margin-bottom: 6px;

    & button {
      color: white;
      border: none;
      font-size: 13px;
      &:hover {
        color: white !important;
      }
    }
  }
`;

const Flex = styled.div`
  display: flex;
  gap: 6px;
  padding-top: 5px;
  padding-bottom: 5px;
  background-color: white;
  padding: 4px;
  margin-bottom: 6px;
  align-items: stretch;

  & .cancel-btn {
    background-color: #fff;
    color: #d3a706;
    & i {
      font-size: 14px;
    }
    &:hover {
      background-color: #fff !important;
      color: #d3a706 !important;
      opacity: 0.8;
    }
  }
  & .add-new {
    background-color: white;
    border: 1px solid green;
    color: green;
    &:hover {
      background-color: white !important;
      color: green !important;
      opacity: 0.8;
    }
  }
  & .p-datatable {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }

  & .title-container {
    width: fit-content;
  }
  & .hyperlink {
    cursor: pointer;
    /* border: 1px solid #eee; */
    overflow: auto;

    & .ag-root-wrapper {
      border-radius: 0px;
    }
  }

  & .actions {
    width: 50px;
    flex: unset;
    display: flex;
    gap: 3px;

    & button {
      font-size: 12px;
      width: 100%;
      height: 28px;
      padding: 0px;
      box-shadow: none;

      &:disabled {
        color: white;
        opacity: 0.8;
      }
    }
  }
  & .item {
    cursor: pointer;
    padding: 5px 2px;
    display: flex;
    gap: 4px;
    align-items: center;
    flex-direction: row;

    &:hover {
      text-decoration: underline;
      color: #6363d0;
    }
  }

  & > div:first-child {
    flex: 1;
  }
  & > div:nth-child(2) {
    flex: 2;
  }
  & > div {
    height: fit-content;
    & h6 {
      max-width: 100%;
    }
  }
  & .data-table-wrapper {
    height: auto !important;
  }
`;
