import { styled } from "@linaria/react";
import { BreadCrumb, GraphComponent } from "../../components";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { setBreadcrumb, setParentBreadcrumbs } from "../../store/features";

const GraphPage = () => {
  const breadcrumb = [
    {
      label: "",
      to: "/",
    },
  ];
  const params = useParams();

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));
  }, []);

  return (
    <Main>
      <BreadCrumb />

      <Wrapper>
        <Container>
          <GraphComponent fromTrashcan={false} id={params?.nodeId} />
          {/* <RightDiv>
            <BottomNavigationMenu
              noGraph
              maxHeight={"75vh"}
              id={params?.nodeId}
            />
          </RightDiv> */}
        </Container>
      </Wrapper>
    </Main>
  );
};

export default GraphPage;

const Main = styled.div`
  overflow: hidden;
  height: 100%;
  width: 100%;
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
`;

const Wrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f3f2ed;
  height: calc(100% - 32px);
`;
