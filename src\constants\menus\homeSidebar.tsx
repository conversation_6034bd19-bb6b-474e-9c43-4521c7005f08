import {
  ClockCircleFilled,
  Clock<PERSON>ir<PERSON>Outlined,
  ContainerFilled,
  Container<PERSON>utlined,
  DeleteFilled,
  <PERSON>Filled,
  MessageFilled,
  MessageOutlined,
  P<PERSON>pinFilled,
  SettingFilled,
  SettingOutlined,
} from "@ant-design/icons";

export const getActiveIcon = (key) => {
  switch (key) {
    case "message":
      return <ContainerFilled />;
    case "favorite":
      return <PushpinFilled />;
    // case "grades":
    //   return <StarFilled />;
    case "comments":
      return <MessageFilled />;
    case "history":
      return <ClockCircleFilled />;
    case "newsletter":
      return <MailFilled />;
    case "settings":
      return <SettingFilled />;
    case "trashcan":
      return <DeleteFilled />;
  }
};
export const homeSidebarItems = [
  {
    key: "message",
    label: "Message",
    icon: <ContainerOutlined id="message-item" />,
    allowedroles: ["admin", "user"],
  },

  // {
  //   key: "grades",
  //   label: "My Grades",
  //   icon: <StarOutlined id="grades-item" />,
  // },
  {
    key: "comments",
    label: "My Comments",
    icon: <MessageOutlined id="comments-item" />,
    allowedroles: ["admin", "user"],
  },

  {
    key: "history",
    label: "History",
    icon: <ClockCircleOutlined id="history-item" />,
    allowedroles: ["admin", "user"],
  },
  // {
  //   key: "newsletter",
  //   label: "Newsletter",
  //   icon: <MailOutlined id="newsletter-item" />,
  // },
  {
    key: "settings",
    label: "Settings",
    icon: <SettingOutlined id="settings-item" />,
    allowedroles: ["admin", "user", "no-person"],
  },
  {
    key: "trashcan",
    label: "Trashcan",
    allowedroles: ["admin", "user"],
    icon: (
      <span style={{ color: "red" }}>
        <DeleteFilled id="trashcan-item" />
      </span>
    ),
  },
];
