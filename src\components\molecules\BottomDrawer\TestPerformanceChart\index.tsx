import { styled } from "@linaria/react";
import { Chart } from "primereact/chart";
import { useQuery } from "react-query";
import { useSearchParams } from "react-router-dom";
import { getDQMTestResults } from "../../../../services";
import {
  GET_DQM_TEST_DATA,
  GET_NODE_ATTRIBUTES_DETAILS,
} from "../../../../constants";
import { memo, useMemo } from "react";
import "chartjs-plugin-zoom";
import { ProgressSpinner } from "primereact/progressspinner";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { INodeDetails } from "../../../../interfaces";
import { getNodeDetails } from "../../../../services/node";

// Display test performance chart on bottom drawer

const TestPerformanceChart = memo(() => {
  const [searchParams] = useSearchParams();

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const { data: bodyDetails } = useQuery<INodeDetails, any>(
    [GET_NODE_ATTRIBUTES_DETAILS, searchParams.get("nodeId")],
    () => getNodeDetails(searchParams.get("nodeId")),
    {
      staleTime: Infinity,
    }
  );

  const { data: chartData, isLoading } = useQuery(
    [GET_DQM_TEST_DATA, searchParams.get("nodeId")],
    () => getDQMTestResults(searchParams.get("nodeId"))
  );

  const selectedTemplateAttributes =
    templatesData[bodyDetails?.templateId]?.attributeTemplates;

  // Get the ALARM_THRESHOLD and WARNING_THRESHOLD attributes
  const ALARM_THRESHOLD = bodyDetails?.body?.find(
    (attr) =>
      attr.id ===
      selectedTemplateAttributes?.find(
        (attr) => attr.attributeValueType === "ALARM_THRESHOLD"
      )?.id
  );

  // Get the WARNING_THRESHOLD attribute
  const WARNING_THRESHOLD = bodyDetails?.body?.find(
    (attr) =>
      attr.id ===
      selectedTemplateAttributes?.find(
        (attr) => attr.attributeValueType === "WARNING_THRESHOLD"
      )?.id
  );

  // Get the precision attribute
  const precision = bodyDetails?.body?.find(
    (attr) => attr.name === "Precyzja"
  )?.value;

  const lineData = useMemo(() => {
    return {
      labels: chartData?.map((chart) =>
        dayjs.utc(chart.testTimestamp).format("YYYY-MM-DD HH:mm:ss")
      ),
      datasets: [
        {
          label: ALARM_THRESHOLD?.name,
          data: chartData?.map(() => ALARM_THRESHOLD.value * 100),
          fill: false,
          pointRadius: 0,
          borderColor: "red", // Line color
          tension: 0.4, // Line smoothness
          borderWidth: 2,
        },

        {
          label: WARNING_THRESHOLD?.name,
          data: chartData?.map(() => WARNING_THRESHOLD.value * 100),
          fill: false,
          pointRadius: 0,
          borderColor: "orange", // Line color
          tension: 0.4, // Line smoothness
          borderWidth: 2,
        },

        {
          label: "[Lbr / Lpr] (%)",
          data: chartData?.map((chart) =>
            ((chart.errorRecords / chart.sampleRecords) * 100).toFixed(
              precision || 2
            )
          ),
          fill: false,
          pointRadius: 4,
          borderColor: "#4377a2", // Line color
          tension: 0.4, // Line smoothness
          borderWidth: 2,
        },
      ],
    };
  }, [chartData, precision, WARNING_THRESHOLD, ALARM_THRESHOLD]);

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 800, // Duration of the animation (in milliseconds)
      easing: "easeOutQuad", // Easing function to control animation speed
    },
    aspectRatio: 1,
    plugins: {
      zoom: {
        zoom: {
          wheel: {
            enabled: true,
          },
          pinch: {
            enabled: true,
          },
          mode: "x",
        },
        pan: {
          enabled: true,
          mode: "x",
        },
      },

      legend: {
        position: "bottom",
        labels: {
          usePointStyle: true, // Enables point styling
          pointStyle: "line", // Makes the legend item a line
        },
      },
      tooltip: {
        mode: "nearest",
        intersect: false,
        titleFont: {
          family: "Poppins, sans-serif",
        },
        padding: 10,
        displayColors: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false, // Shows the vertical grid (can leave as true or adjust as needed)
        },
        ticks: {
          font: {
            family: "'Poppins', sans-serif", // Font family for X-axis labels
            size: 13, // Font size for X-axis labels
          },
        },
      },
      y: {
        beginAtZero: true,
        ticks: {
          font: {
            family: "'Poppins', sans-serif", // Font family for X-axis labels
            size: 13, // Font size for X-axis labels
          },
        },
      },
    },
  };
  return (
    <Wrapper>
      {isLoading ? (
        <Loader>
          <ProgressSpinner />
        </Loader>
      ) : (
        <Chart type="line" options={lineOptions} data={lineData} />
      )}
    </Wrapper>
  );
});

export { TestPerformanceChart };

const Loader = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  & .p-progress {
    height: 50px;
  }
`;

const Wrapper = styled.div`
  height: 100%;
  overflow: auto;
  padding: 8px;

  & .p-chart {
    height: 100%;
  }
`;
