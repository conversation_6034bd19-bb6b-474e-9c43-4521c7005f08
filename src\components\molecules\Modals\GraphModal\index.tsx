import { styled } from "@linaria/react";
import { Modal } from "antd";
import { Chart } from "primereact/chart";

const GraphModal = ({ isOpen, onClose, lineData }) => {
  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 800, // Duration of the animation (in milliseconds)
      easing: "easeOutQuad", // Easing function to control animation speed
    },
    aspectRatio: 1,
    plugins: {
      legend: {
        position: "bottom",
        labels: {
          usePointStyle: true, // Enables point styling
          pointStyle: "line", // Makes the legend item a line
        },
      },
      tooltip: {
        mode: "nearest",
        intersect: false,
        titleFont: {
          family: "Poppins, sans-serif",
        },
        padding: 10,
        displayColors: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false, // Shows the vertical grid (can leave as true or adjust as needed)
        },
        ticks: {
          font: {
            family: "'Poppins', sans-serif", // Font family for X-axis labels
            size: 13, // Font size for X-axis labels
          },
        },
      },
      y: {
        beginAtZero: true,
        ticks: {
          font: {
            family: "'Poppins', sans-serif", // Font family for X-axis labels
            size: 13, // Font size for X-axis labels
          },
        },
      },
    },
  };

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      centered
      title={"Chart"}
      width="80%"
    >
      <Wrapper>
        <Chart type="line" options={lineOptions} data={lineData} />
      </Wrapper>
    </Modal>
  );
};

export { GraphModal };

const Wrapper = styled.div`
  padding-bottom: 20px;
  position: relative;
  height: 100%;
  min-height: 400px;
`;
