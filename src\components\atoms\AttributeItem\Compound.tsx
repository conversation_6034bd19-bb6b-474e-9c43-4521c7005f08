import { styled } from "@linaria/react";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { Empty } from "antd";
import { Hyperlink } from "../../atoms/AttributeItem/Hyperlink";
import { useQueryClient } from "react-query";
import { GET_NODE_ATTRIBUTES_DETAILS } from "../../../constants";
import { useSearchParams } from "react-router-dom";
import { INodeDetails } from "../../../interfaces";

const Compound = ({ id, val }) => {
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();

  let templateId = null;
  if (searchParams.get("template")) {
    templateId = searchParams.get("template");
  } else {
    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      searchParams.get("nodeId"),
    ]) as INodeDetails;
    templateId = nodeDetails?.templateId;
  }

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const selectedTemplate = templatesData[templateId]?.attributeTemplates;

  const attributes = selectedTemplate?.find((attr) => attr?.id === id);

  return (
    <Wrapper>
      <Flex>
        <div className="list">
          <h6>{attributes?.nameList1}</h6>
        </div>
        <div className="list">
          <h6>
            {attributes?.nameList2} ({attributes?.multiplicityList2})
          </h6>
        </div>
      </Flex>

      {val.length === 0 ? (
        <Flex>
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={false} />
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={false} />
          <div className="actions" />
        </Flex>
      ) : (
        val?.map((selected, index) => (
          <Flex
            key={`${selected.id}-${index}`}
            style={{ borderBottom: "1px solid #eee" }}
          >
            <div className="hyperlink">
              <Hyperlink
                key={`table-${selected.id}-${index}`}
                val={[selected]}
              />
            </div>
            <div className="hyperlink">
              {selected.value?.length > 0 ? (
                <Hyperlink
                  key={`table-child-${selected.id}-${index}`}
                  val={[...selected.value]}
                />
              ) : (
                <p className="no-data">—</p>
              )}
            </div>
          </Flex>
        ))
      )}
    </Wrapper>
  );
};

export { Compound };

const Wrapper = styled.div`
  margin: -6px;
  padding: 6px 6px 1px 12px;
  background-color: var(--color-light);

  & .ant-tree-switcher {
    display: none;
  }
`;

const Flex = styled.div`
  display: flex;
  gap: 6px;
  padding-top: 5px;
  padding-bottom: 5px;
  background-color: white;
  padding: 4px;
  margin-bottom: 6px;
  align-items: flex-start;

  & .title-container {
    width: fit-content;
  }
  & .hyperlink {
    cursor: pointer;
    /* border: 1px solid #eee; */
    overflow: auto;

    & .ag-root-wrapper {
      border-radius: 0px;
    }
  }

  & .no-data {
    padding: 6px;
    font-size: 12px;
  }
  & .actions {
    width: 90px;
    flex: unset;
    display: flex;
    flex-direction: column;
    gap: 8px;

    & button {
      font-size: 12px;
      width: 100%;
      height: 28px;
      box-shadow: none;
    }
  }
  & .item {
    cursor: pointer;
    padding: 5px 2px;
    display: flex;
    gap: 4px;
    align-items: center;
    flex-direction: row;

    &:hover {
      text-decoration: underline;
      color: #6363d0;
    }
  }
  & > div {
    flex: 1;

    & h6 {
      max-width: 100%;
    }
  }
`;
