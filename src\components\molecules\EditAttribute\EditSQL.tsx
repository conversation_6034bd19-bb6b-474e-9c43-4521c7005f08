import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css";
import Editor from "react-simple-code-editor";

const EditSQL = ({ val, onValueChange }) => {
  return (
    <Editor
      className="sql-editor"
      value={val}
      onValueChange={onValueChange}
      highlight={(code) => highlight(code || "", languages.js)}
      style={{
        fontFamily: '"Fira code", "Fira Mono", monospace',
      }}
    />
  );
};

export { EditSQL };
