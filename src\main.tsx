// Ensure Vite pre-bundles these dependencies and their locale
import "dayjs";
import "dayjs/locale/pl";
import zoomPlugin from "chartjs-plugin-zoom";
import ReactDOM from "react-dom/client";
import { <PERSON>rowserRouter } from "react-router-dom";
import App from "./App";
import "./index.css";
import "./utils/i18n";
import { store } from "./store";
import { Provider } from "react-redux";
import { MasterDetailModule } from "ag-grid-enterprise";
import {
  ClientSideRowModelModule,
  ColumnApiModule,
  ModuleRegistry,
  RenderApiModule,
  RowApiModule,
  RowSelectionModule,
  TextFilterModule,
  ValidationModule,
  CsvExportModule,
  RowStyleModule,
  RowAutoHeightModule,
  LocaleModule,
  CellSpanModule,
  TextEditorModule,
  ClientSideRowModelApiModule,
} from "ag-grid-community";
ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  RowStyleModule,
  ValidationModule,
  RowApiModule,
  RowSelectionModule,
  LocaleModule,
  ClientSideRowModelApiModule,
  CellSpanModule,
  TextEditorModule,
  RowAutoHeightModule,
  MasterDetailModule,
  TextFilterModule,
  ColumnApiModule,
  RenderApiModule,
  CsvExportModule,
]);

// import { initSentry } from "./sentry";

import "primereact/resources/themes/lara-light-blue/theme.css";
import "primeicons/primeicons.css";

import Chart from "chart.js/auto";

Chart.register(zoomPlugin);

// initSentry();
ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <Provider store={store}>
    <BrowserRouter basename={import.meta.env.VITE_APP_BASE_URL}>
      <App />
    </BrowserRouter>
  </Provider>
);
