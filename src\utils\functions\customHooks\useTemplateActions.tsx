import { getAttributeIcon } from "../../../constants";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useTemplateActions = () => {
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const getTemplateIcon = (templateId: number) => {
    if (!templatesData) {
      return "-";
    }

    let icon = "_30_folder";
    const selectedTemplate = templatesData[Number(templateId)];
    if (selectedTemplate) {
      icon = selectedTemplate.icon;
    }
    return getAttributeIcon(icon);
  };

  const getTemplateName = (templateId: number) => {
    if (!templatesData) {
      return "-";
    }
    let name = "";
    const selectedTemplate = templatesData[Number(templateId)];
    if (selectedTemplate) {
      name = selectedTemplate.name;
    }
    return name;
  };

  const getAllowedChildrens = (templateId: number) => {
    let allowedChildrens = [];

    const selectedTemplate = templatesData[Number(templateId)];

    if (selectedTemplate) {
      allowedChildrens = selectedTemplate.allowedChildren;
    }
    return allowedChildrens;
  };

  return { getTemplateIcon, getTemplateName, getAllowedChildrens };
};

export { useTemplateActions };
