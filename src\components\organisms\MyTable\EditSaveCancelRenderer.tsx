import { EditOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

const EditSaveCancelRenderer = (props) => {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const handleEditingStarted = (event) => {
      if (event.rowIndex === props.node.rowIndex) {
        setIsEditing(true);
      }
    };

    const handleEditingStopped = (event) => {
      if (event.rowIndex === props.node.rowIndex) {
        setIsEditing(false);
      }
    };

    props.api.addEventListener("cellEditingStarted", handleEditingStarted);
    props.api.addEventListener("cellEditingStopped", handleEditingStopped);

    return () => {
      props.api.removeEventListener("cellEditingStarted", handleEditingStarted);
      props.api.removeEventListener("cellEditingStopped", handleEditingStopped);
    };
  }, [props.api, props.node.rowIndex]);

  const handleEdit = () => {
    props.api.startEditingCell({
      rowIndex: props.node.rowIndex,
      colKey: props.column.getColId(),
    });
  };

  const handleSave = () => {
    props.api.stopEditing(false); // Save changes
  };

  const handleCancel = () => {
    props.api.stopEditing(true); // Cancel changes
  };

  return (
    <div className="ag-grid-edit-actions">
      {!isEditing && (
        <Tooltip title={t("Edit")}>
          <EditOutlined onClick={handleEdit} />
        </Tooltip>
      )}
      {isEditing && (
        <>
          <Tooltip title={t("Update")}>
            <Button
              type="primary"
              onClick={handleSave}
              className="save-action"
              icon={<i className="pi pi-save" />}
            />
          </Tooltip>
          <Tooltip title={t("Cancel")}>
            <Button
              type="primary"
              className="cancel-action"
              icon={<i className="pi pi-ban" />}
              onClick={handleCancel}
            />
          </Tooltip>
        </>
      )}
    </div>
  );
};

export { EditSaveCancelRenderer };
