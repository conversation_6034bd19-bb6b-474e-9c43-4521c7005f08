import { Dropdown, Flex } from "antd";
import { NODES_MENU_ITEMS, TRASH_NODES_MENU_ITEMS } from "../../../constants";
import { getParentID } from "../../../utils/functions/getParentID";
import { useState } from "react";
import { useHyperlinkActions } from "../../../utils/functions/customHooks";
import { DetailsContainer } from "../..";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

const NodeNameRenderer = (params) => {
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();
  const record = params.data;

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  return (
    <Flex vertical gap={8}>
      <Dropdown
        menu={{
          items: record?.data?.inTrash
            ? NODES_MENU_ITEMS
            : TRASH_NODES_MENU_ITEMS,
          onClick: (e) => handleNodeClick(e.key, record.id, record.name),
        }}
        trigger={["contextMenu"]}
      >
        <p
          className={`title-container ${
            record.inTrash ? "trash-hyperlink" : ""
          }`}
          onClick={async (e) => {
            e.stopPropagation();
            handleHyperlinkAction({
              id: record.id,
              inTrash: record.inTrash,
            });
          }}
        >
          {record.name}
        </p>
      </Dropdown>
      {/* 
      {!Object.keys(expandedRows || {})?.includes(record?.id.toString()) &&
        record?.attributeLookup?.map((lookup) => (
          <AttributeItem
            readOnly
            value={lookup?.value}
            type={lookup?.type}
            key={lookup.id}
            title={lookup.name}
            titleClassName={TITLE_CLASSNAME}
          />
        ))} */}

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Flex>
  );
};

export { NodeNameRenderer };
