import { styled } from "@linaria/react";
import { Breadcrumb } from "antd";
import { useEffect, useState } from "react";
import { AttributeItem } from "../../atoms";
import { StyledModal } from "../../molecules";
import { useQuery } from "react-query";
import { getHierarchyDetails, getNodeDetails } from "../../../services/node";
import { IAttributes } from "../../../interfaces";
import { LoadingOutlined } from "@ant-design/icons";
import { getAttributeTitleWidth, useTheme } from "../../../utils";
import { css } from "@linaria/core";
import { GET_ATTRIBUTES, GET_HIERARCHY_DETAILS } from "../../../constants";
import { BottomNavigationDrawer } from "../BottomNavigationDrawer";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { setBottomDrawerMask } from "../../../store/features";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  id: string;
}

const DetailsContainer = ({ isOpen, onClose, title, id }: Props) => {
  const theme = useTheme();
  const dispatch = useDispatch();

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const [attributes, setAttributes] = useState([]);
  const [breadcrumb, setBreadcrumb] = useState([]);

  const { isLoading } = useQuery(
    [GET_ATTRIBUTES, id],
    () => getNodeDetails(id),
    {
      onSuccess: (nodeDetails) => {
        const attributes = [];
        const selectedTemplateAttributes =
          templatesData[nodeDetails.templateId]?.attributeTemplates || [];

        selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
          const attributeValue = nodeDetails?.body?.find(
            (item) => item.id == attribute.id
          );
          if (attributeValue) {
            attributes.push({
              ...attributeValue,
              ...attribute,
              value:
                attribute.type === "multiplicity"
                  ? {
                      text1: attributeValue?.value?.split("..")[0],
                      text2: attributeValue?.value?.split("..")[1],
                    }
                  : attribute.type === "switch"
                  ? attributeValue?.value || false
                  : attributeValue?.value,
            });
          }
        });

        setAttributes([...attributes]);
      },

      enabled: !!id,
    }
  );

  useQuery([GET_HIERARCHY_DETAILS, id], () => getHierarchyDetails(id), {
    enabled: !!id,
    onSuccess: (data) => {
      const breadcrumbs = [];
      data.path.forEach((path) => {
        if (!path?.virtual)
          breadcrumbs.push({
            title: path.value,
            disabled: true,
            id: path.key,
          });
      });

      setBreadcrumb([
        ...breadcrumbs,
        {
          title: title,
        },
      ]);
    },
  });

  const bottomDrawerMask = useSelector(
    (root: RootState) => root.mask.bottomDrawer
  );

  const TITLE_CLASSNAME = "details-container-title";

  useEffect(() => {
    const titles = document.querySelectorAll(`.${TITLE_CLASSNAME}`) as any;

    titles.forEach((title) => {
      title.style.width = `fit-content`;
    });

    const maxTitleWidth = getAttributeTitleWidth(`.${TITLE_CLASSNAME}`);
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });
  }, [attributes]);

  return (
    <StyledModal
      onCancel={() => {
        dispatch(setBottomDrawerMask(false));
        onClose();
      }}
      open={isOpen}
      contentClassName={ContentCSS}
      customHeaderStyles={headerCSS}
      title={
        <Breadcrumb
          separator=">"
          className="main-breadcrumbs"
          items={breadcrumb}
        />
      }
      size={{ height: "50vw", width: "80vw" }}
    >
      <Wrapper theme={theme}>
        <div id="details-parent-container">
          <Content>
            {bottomDrawerMask && <div className="mask" />}
            {isLoading ? (
              <div className="loader">
                <LoadingOutlined />
              </div>
            ) : (
              attributes.map((item, index) => {
                return (
                  <AttributeItem
                    readOnly
                    key={index}
                    {...item}
                    title={item.name}
                    titleClassName={TITLE_CLASSNAME}
                  />
                );
              })
            )}
          </Content>
          <BottomNavigationDrawer
            displaySaveCancel={bottomDrawerMask}
            fromModal
            noGraph
            id={id}
          />
        </div>
      </Wrapper>
    </StyledModal>
  );
};

export { DetailsContainer };

const ContentCSS = css`
  padding: 0px;
  overflow: hidden;
`;

const headerCSS = css`
  background: var(--bg-secondary);
  & li {
    color: #fff;
  }

  & .ant-breadcrumb-separator {
    color: #fff !important;
  }

  & .p-dialog-header-icons button {
    color: #fff;

    &:hover {
      color: var(--bg-secondary);
    }
  }
  & ol > li:last-child .ant-breadcrumb-link {
    color: #fff100 !important;
    font-weight: 600;
  }
`;
const Wrapper = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  & #details-parent-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: calc(100% - 34px);
  }

  & th {
    color: ${({ theme }) => theme?.colorPrimary} !important;
    background: ${({ theme }) => theme?.bgAttributes} !important;
  }

  & .breadcrumb {
    margin: 0px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
  }

  & .loader {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 40px;
    height: 50vh;
  }

  & .ant-drawer-content {
    border-radius: 10px;
  }
  & .ant-float-btn {
    position: absolute;
  }
  & > div:last-child {
    & > div {
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;
    }

    & ul {
      border-bottom-left-radius: 8px;
    }
  }
`;

const Content = styled.div`
  padding: 10px 20px;
  overflow: auto;
  position: relative;
  flex: 1;
  & .mask {
    inset: 0px;
    height: 100%;
    width: 100%;
  }
  background-color: #fff;
`;
