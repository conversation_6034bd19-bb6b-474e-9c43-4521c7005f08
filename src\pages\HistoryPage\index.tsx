import { LoadingOutlined } from "@ant-design/icons";
import { Ava<PERSON>, Button, Flex, Tag } from "antd";
import { useTheme } from "../../utils/useTheme";
import { Suspense, useEffect, useState } from "react";
import { Wrapper } from "./style";
import DummyIcon from "../../assets/images/dummy.jpeg";
import { BreadCrumb, MyTable } from "../../components";
import { GET_LOCAL_SETTINGS_KEY } from "../../constants";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useNotification } from "../../utils/functions/customHooks";
import { ILocalSettings } from "../../interfaces";
import { saveLocalSettings } from "../../services";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { RootState } from "../../store";
import dayjs from "dayjs";

const HistoryPage = () => {
  const theme = useTheme();
  const PAGE_SIZE = 50;
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [initialColumns, setInitialColumns] = useState([]);
  const [allColumnsRequest, setAllColumnsRequest] = useState(COLUMNS);
  // const [filters, setFilters] = useState({});
  // const [sort, setSort] = useState({
  //   field: null,
  //   order: null,
  // });

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));

    const newData = [];
    for (let i = data.length; i < PAGE_SIZE + data.length; i++) {
      newData.push({
        key: i,
        id: i,
        date: new Date("2022-03-01 08:26:17"),
        user: i % 2 === 0 ? "Rafał Kalinowski" : "Aiska Basnet",
        object: "Business term",
        attribute: "Status",
        oldvalue: "In progress",
        newvalue: "For acceptance",
        name: i % 2 === 0 ? "Basnet Aiska" : "Aiska Basnet",
        userImage: DummyIcon,
        version: "Current version",
        lastViewed: new Date("2022-03-01 08:26:17"),
      });
    }

    setData([...data, ...newData]);
    setLoading(false);

    // setFilters(getInitialTableFilters(COLUMNS));
  }, []);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.historyTable &&
      localSettingsData?.body[0]?.value?.historyTable?.columns
    ) {
      const allColumns = [];
      localSettingsData.body[0].value.historyTable.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({ ...COLUMNS[index] });
        }
      );
      setInitialColumns(allColumns);

      // if (localSettingsData?.body[0]?.value?.historyTable?.filters) {
      //   setFilters(
      //     deepClone(localSettingsData?.body[0]?.value?.historyTable?.filters)
      //   );
      // }
      // if (localSettingsData?.body[0]?.value?.historyTable?.sort) {
      //   setSort(localSettingsData?.body[0]?.value?.historyTable?.sort);
      // }
    } else {
      setInitialColumns(COLUMNS);
    }
  }, [localSettingsData]);

  const detectChange = () => {
    if (!mask) {
      dispatch(setMask(true));
    }
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        historyTable: {
          columns: allColumnsRequest,
          // filters: filters,
          // sort: sort,
        },
      },
    };
    mutation.mutate(request);
  };

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const handleCancel = () => {
    dispatch(setMask(false));
    // setFilters(
    //   localSettingsData?.body[0]?.value?.historyTable?.filters
    //     ? deepClone(localSettingsData?.body[0]?.value?.historyTable?.filters)
    //     : getInitialTableFilters(COLUMNS)
    // );
    // setSort(
    //   localSettingsData?.body[0]?.value?.historyTable?.sort || {
    //     field: null,
    //     order: null,
    //   }
    // );
    setResetTrigger((trigger) => trigger + 1);
  };

  return (
    <Suspense fallback={<LoadingOutlined />}>
      <Wrapper theme={theme}>
        {contextHolder}
        <BreadCrumb
          extra={
            <Flex gap={10}>
              {mask && (
                <>
                  <Button
                    className="breadcrumb-button cancel-button"
                    type="primary"
                    onClick={handleCancel}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    className="breadcrumb-button save-button"
                    type="primary"
                    onClick={handleSave}
                    loading={mutation.isLoading}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </Flex>
          }
        />

        {data.length === 0 || initialColumns.length === 0 ? (
          <div className="initial-loading">
            <LoadingOutlined />
          </div>
        ) : (
          <div
            className="content"
            style={{ border: mask ? "1px solid red" : "none" }}
          >
            <MyTable
              loading={loading}
              emptyMessage="No history"
              height={"calc(100vh - 165px)"}
              data={data}
              resetTrigger={resetTrigger}
              columns={initialColumns}
              setColumnsRequest={setAllColumnsRequest}
              detectChange={detectChange}
              excelFileName="history"
            />
          </div>
        )}
      </Wrapper>
    </Suspense>
  );
};

export default HistoryPage;

const COLUMNS = [
  {
    headerName: "Name",
    field: "name",
    width: 200,
  },
  {
    headerName: "Version",
    field: "version",
    width: 200,
  },
  {
    headerName: "User",
    field: "user",
    width: 200,
    cellRenderer: ({ data }) => (
      <>
        <Avatar size={32} src={data.userImage} /> {data.user}
      </>
    ),
  },
  {
    headerName: " Last Viewed",
    field: "date",
    isDate: true,
    width: 200,
    cellRenderer: ({ data }) => {
      return dayjs(data?.date).format("YYYY/MM/DD HH:mm");
    },
  },
  {
    headerName: "Attribute",
    field: "attribute",
    cellRenderer: ({ data }) => <Tag>{data.attribute}</Tag>,
    width: 200,
  },
  {
    headerName: "Old Value",
    field: "oldvalue",
    width: 200,
  },
  {
    headerName: "New Value",
    field: "newvalue",
    width: 200,
  },
];

const breadcrumb = [
  {
    title: "History",
    to: "/history",
    mockup: true,
  },
];
