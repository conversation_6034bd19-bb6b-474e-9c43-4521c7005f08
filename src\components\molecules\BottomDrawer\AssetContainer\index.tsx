import { styled } from "@linaria/react";
import { Dropdown, Flex } from "antd";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  GET_ASSETS,
  GET_COUNTERS,
  GET_LOCAL_SETTINGS_KEY,
  NODES_MENU_ITEMS,
  TRASH_NODES_MENU_ITEMS,
} from "../../../../constants";
import { DetailsContainer, MyTable } from "../../../organisms";
import { getParentID, transformObjectPath } from "../../../../utils";
import { useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { DELETED_FLAG, ILocalSettings } from "../../../../interfaces";
import { getAssets, saveLocalSettings } from "../../../../services";
import { setBottomDrawerMask } from "../../../../store/features";
import {
  useFlags,
  useHyperlinkActions,
  useNotification,
} from "../../../../utils/functions/customHooks";
import { useTranslation } from "react-i18next";
import { setTrashcanDrawerMask } from "../../../../store/features/trashcan";
import { DeleteNodeModal } from "../../Modals";
import { Button } from "primereact/button";
import { debounce } from "lodash";
import { RootState } from "../../../../store";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

// Display assets in metamodel

const AssetContainer = ({ id, fromTrashcan, displaySaveCancel }) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();

  const parentRef = useRef(null);
  const { getFlags } = useFlags();

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [height, setHeight] = useState(0);
  const [triggerChange, setTriggerChange] = useState(0);
  const [selectedRelations, setSelectedRelations] = useState([]);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [deleteAction, setDeleteAction] = useState(null);

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );
  const bottomNavigationMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  // Fetching assets
  const {
    isLoading,
    data: assetsData,
    isFetching,
    isError,
  } = useQuery([GET_ASSETS, id], () => getAssets(id), {
    enabled: !!id && !searchParams.get("draft"),
  });

  // dynamic height of container
  useEffect(() => {
    const parent = parentRef.current;
    if (!parent) return;

    const updateHeight = debounce((entries) => {
      for (const entry of entries) {
        setHeight(entry.contentRect.height);
      }
    }, 100);

    const observer = new ResizeObserver(updateHeight);
    observer.observe(parent);

    return () => {
      observer.disconnect();
    };
  }, []);

  const COLUMNS = useMemo(() => {
    return [
      {
        headerName: t("Asset Name"),
        field: "name",
        flex: 1,
        cellRenderer: "agGroupCellRenderer",
        cellRendererParams: {
          innerRenderer: (params) => {
            const record = params?.data;
            return (
              <Flex vertical gap={8}>
                <Dropdown
                  menu={{
                    items: record?.flag?.includes(DELETED_FLAG)
                      ? TRASH_NODES_MENU_ITEMS
                      : NODES_MENU_ITEMS,
                    onClick: (e) =>
                      handleNodeClick(e.key, record.id, record.name),
                  }}
                  trigger={["contextMenu"]}
                >
                  <p
                    className={`title-container ${
                      record?.flag?.includes(DELETED_FLAG)
                        ? "trash-hyperlink"
                        : ""
                    }`}
                    onClick={async (e) => {
                      e.stopPropagation();
                      if (!bottomNavigationMask)
                        handleHyperlinkAction({
                          id: record.id,
                          inTrash: record?.flag?.includes(DELETED_FLAG),
                          isAsset: true,
                        });
                    }}
                  >
                    {record.name}
                  </p>
                </Dropdown>
              </Flex>
            );
          },
        },
      },

      {
        headerName: "Path",
        field: "pathName",
        minWidth: 200,
        flex: 1,
        cellRenderer: ({ data }) => (
          <p className="right-align">
            {data?.pathName
              ? transformObjectPath(
                  data?.pathName,
                  data?.flag?.includes(DELETED_FLAG)
                )
              : "-"}
          </p>
        ),
      },
    ];
  }, [isFetching, bottomNavigationMask]);

  useEffect(() => {
    if (!assetsData) {
      return;
    }
    const updatedData = assetsData.map((item) => {
      const hasAttributes =
        templatesData[item.templateId]?.attributeTemplates?.length > 0;
      return {
        ...item,
        templateHasAttributes: hasAttributes,
      };
    });
    setData(updatedData);
    setTriggerChange((key) => key + 1);
  }, [assetsData, templatesData]);

  useEffect(() => {
    setTriggerChange((key) => key + 1);
  }, [bottomNavigationMask]);

  const detectChange = () => {
    dispatch(setBottomDrawerMask(true));
  };

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.assetsDrawer &&
      localSettingsData?.body[0]?.value?.assetsDrawer?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.assetsDrawer?.pinned || [];
      const sort = localSettingsData?.body[0]?.value?.assetsDrawer?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.assetsDrawer.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );

      setColumns(allColumns);

      setTriggerChange((prev) => (!prev ? 1 : prev + 1));
    } else {
      setColumns(COLUMNS);
    }
  }, [localSettingsData]);

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }
      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const handleCancel = () => {
    setTimeout(() => {
      setTriggerChange((prev) => (!prev ? 1 : prev + 1));
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
    }, 200);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setBottomDrawerMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
    },
  });

  const handleSave = (newColumns: string[], filters, sort, pinned) => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        assetsDrawer: {
          columns: newColumns,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  // disbale selection of deleted assets
  const disableSelection = (record) => {
    return !record?.data?.flag?.includes(DELETED_FLAG);
  };

  const handleDeleteClick = useCallback(() => {
    if (selectedRelations?.length === 0) {
      return;
    }

    if (selectedRelations?.length === 1) {
      const selected = selectedRelations[0];

      setDeleteAction({
        allowedChildren: undefined,
        id: selected?.id,
        key: "delete-all",
        label: selected?.name,
        parentId: selected?.parentId,
        templateId: selected?.templateId,
        isLeaf: selected?.last,
      });
    } else {
      setDeleteAction({
        allowedChildren: undefined,
        id: null,
        key: "delete-all",
        label: "",
        parentId: null,
        templateId: null,
      });
    }
  }, [selectedRelations]);

  return (
    <Wrapper
      ref={parentRef}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {contextHolder}
      <MyTable
        excelFileName="assets"
        onSelect={setSelectedRelations}
        loading={isLoading}
        height={`${height - 50}px`}
        isError={isError}
        resetTrigger={triggerChange}
        isRowSelectable={disableSelection}
        columns={columns}
        detectChange={detectChange}
        displaySaveCancel={displaySaveCancel}
        onCancelClick={handleCancel}
        initialFilters={
          localSettingsData?.body[0]?.value?.assetsDrawer?.filters || {}
        }
        extra={
          <Button
            type="button"
            disabled={!selectedRelations?.length}
            label={t("Move to trash can")}
            icon="pi pi-trash"
            onClick={handleDeleteClick}
            className="primary-button cancel-button"
            rounded
          />
        }
        saveLoading={mutation.isLoading}
        emptyMessage="No assets"
        expandCondition={(data) => data?.body?.length > 0}
        onSaveClick={handleSave}
        data={data?.map((item) => ({
          ...item,
          flag: getFlags(item?.bitFlag),
        }))}
      />

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
      {!!deleteAction && (
        <DeleteNodeModal
          isOpen={!!deleteAction}
          id={deleteAction.id}
          action={deleteAction}
          deleteAssets
          selectedAssets={selectedRelations?.map((asset) => asset.id)}
          templateId={deleteAction?.templateId}
          hasAssetTypeNode={false}
          afterDelete={async () => {
            queryClient.invalidateQueries([GET_COUNTERS, id]);
            queryClient.invalidateQueries([GET_ASSETS, id]);
            setSelectedRelations([]);
          }}
          label={deleteAction.label}
          isMultiple={selectedRelations?.length > 1}
          onClose={() => {
            setDeleteAction(null);
          }}
        />
      )}
    </Wrapper>
  );
};
export { AssetContainer };

const Wrapper = styled.div`
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  padding: 10px;

  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
    height: 100%;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & a:hover {
    text-decoration: underline;
  }
  & td {
    cursor: default !important;
  }

  & .title-container {
    display: flex;
    align-items: center;
    gap: 6px;
    text-align: left;

    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
  }
  & a:hover {
    color: #094f8b;
  }
`;
