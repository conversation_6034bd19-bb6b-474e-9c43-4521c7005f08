import React, { Suspense, useEffect } from "react";
import { Route, Routes } from "react-router-dom";
import "./App.css";
import { Layout } from "./components";
import { PrivateRoute } from "./utils/PrivateRoute";
import { RestrictedRoute } from "./utils/RestrictedRoute";
import { LoadingOutlined } from "@ant-design/icons";
import { QueryClient, QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";
import { Flex } from "antd";
import { useDispatch } from "react-redux";
import { setMenuCollapsed } from "./store/features/sidebar";

import HomePage from "./pages/MainHome";
import TranslationsPage from "./pages/Translations";
import PageNotFound from "./pages/404";
import UnderConstruction from "./pages/UnderConstruction";
import DetailsPage from "./pages/Details";
import MessagePage from "./pages/Messages";
import SettingsPage from "./pages/Settings";
import ChangeThemePage from "./pages/ChangeTheme";
import GraphPage from "./pages/Graph";
import HistoryPage from "./pages/HistoryPage";
import FavoritesPage from "./pages/Favorites";
import MyCommentsPage from "./pages/MyComments";
import GeneralSettings from "./pages/Settings/GeneralSettings";
import MenuCreatorPage from "./pages/MenuCreator";
import MetamodelSettingsPage from "./pages/Settings/MetamodelSettings";
import { setTrashCollapsed } from "./store/features/trashcan";
import ResetDatabase from "./pages/ResetDatabase";
import ApplicationPropertiesPage from "./pages/Settings/AppProperties";
import BusinessLogs from "./pages/BusinessLogs";
import TechnicalLogs from "./pages/TechnicalLogs";
import Profile from "./pages/Profile";
import { AdminRoute } from "./utils/AdminRoute";
import { UserRoute } from "./utils/UserRoute";
const LoginPage = React.lazy(() => import("./pages/Login"));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      cacheTime: Infinity,
    },
  },
});

function App() {
  const dispatch = useDispatch();

  useEffect(() => {
    const isCollapsed = localStorage.getItem("menu-collapsed");
    if (isCollapsed) {
      dispatch(setMenuCollapsed(isCollapsed === "1"));
    }

    const isTrashCollapsed = localStorage.getItem("trash-collapsed");
    if (isTrashCollapsed) {
      dispatch(setTrashCollapsed(isTrashCollapsed !== "1"));
    }
  }, []);

  // Private Route all authenticated can user (admin, user and no-person associated user)
  // Restricted Route only for unauthenticated user
  // Admin Route only for admin
  // User Route for user and admins

  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryDevtools initialIsOpen={false} />
      <Suspense
        fallback={
          <Flex align="center" justify="center">
            <LoadingOutlined
              style={{ fontSize: "30px", marginTop: 30, color: "#4377a2" }}
            />
          </Flex>
        }
      >
        <Routes>
          <Route element={<Layout />}>
            <Route path="/roles" element={<UnderConstruction />} />
            <Route path="/user-groups" element={<UnderConstruction />} />
            <Route path="/users" element={<UnderConstruction />} />
            <Route path="/schedules" element={<UnderConstruction />} />
            <Route
              path="/settings/profile"
              element={
                <PrivateRoute>
                  <Profile />
                </PrivateRoute>
              }
            />

            <Route path="/reset-database" element={<ResetDatabase />} />
            <Route path="*" element={<PageNotFound />} />
            <Route
              path="/"
              element={
                <PrivateRoute>
                  <HomePage />
                </PrivateRoute>
              }
            />

            <Route
              path="/settings/translations"
              element={
                <AdminRoute>
                  <TranslationsPage />
                </AdminRoute>
              }
            />

            <Route
              path="/settings/app-properties"
              element={
                <AdminRoute>
                  <ApplicationPropertiesPage />
                </AdminRoute>
              }
            />

            <Route
              path="/details/graph/:nodeId"
              element={
                <UserRoute>
                  <GraphPage />
                </UserRoute>
              }
            />

            <Route
              path="/business-log"
              element={
                <AdminRoute>
                  <BusinessLogs />
                </AdminRoute>
              }
            />

            <Route
              path="/technical-log"
              element={
                <AdminRoute>
                  <TechnicalLogs />
                </AdminRoute>
              }
            />

            <Route
              path="/history"
              element={
                <UserRoute>
                  <HistoryPage />
                </UserRoute>
              }
            />
            {/* <Route
              path="/details/dqm/:nodeId"
              element={
                <PrivateRoute>
                  <DQMPage />
                </PrivateRoute>
              }
            /> */}
            <Route
              path="/message"
              element={
                <UserRoute>
                  <MessagePage />
                </UserRoute>
              }
            />
            <Route
              path="/comments"
              element={
                <UserRoute>
                  <MyCommentsPage />
                </UserRoute>
              }
            />

            <Route
              path="/pinned"
              element={
                <UserRoute>
                  <FavoritesPage />
                </UserRoute>
              }
            />

            <Route
              path="/settings"
              element={
                <PrivateRoute>
                  <SettingsPage />
                </PrivateRoute>
              }
            />

            <Route
              path="/settings/general"
              element={
                <AdminRoute>
                  <GeneralSettings />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/data-sources/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />

            <Route
              path="/settings/users/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />

            <Route
              path="/settings/user-groups/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />

            <Route
              path="/settings/roles/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/actions/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />

            <Route
              path="/settings/metamodel/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/menu-creator"
              element={
                <AdminRoute>
                  <MenuCreatorPage />
                </AdminRoute>
              }
            />

            <Route
              path="/settings/theme"
              element={
                <UserRoute>
                  <ChangeThemePage />
                </UserRoute>
              }
            />

            <Route
              path="/details/:nodeId"
              element={
                <UserRoute>
                  <DetailsPage />
                </UserRoute>
              }
            />

            <Route
              path="/login"
              element={
                <RestrictedRoute>
                  <LoginPage />
                </RestrictedRoute>
              }
            />
            {/* <Route path="/repositories" element={<RepositoryPage />} /> */}
          </Route>
        </Routes>
      </Suspense>
    </QueryClientProvider>
  );
}

export default App;
