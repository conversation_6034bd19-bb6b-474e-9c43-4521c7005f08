import { Layout, Menu, Tooltip } from "antd";
import { useEffect, useState } from "react";
import {
  DeleteFilled,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PushpinFilled,
} from "@ant-design/icons";
import { styled } from "@linaria/react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTheme } from "../../../utils/useTheme";
import {
  getActiveIcon,
  homeSidebarItems,
} from "../../../constants/menus/homeSidebar";
import { ReactComponent as HomeIcon } from "../../../assets/home.svg";
import { getTemplateIcon } from "../../../utils";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  GET_CHILDRENS,
  GET_LOCAL_SETTINGS_KEY,
  NO_USER_ASSOCIATED,
  TRASHCAN_PARENT_NODE_ID,
} from "../../../constants";
import { ILocalSettings, ITreeData } from "../../../interfaces";
import { saveLocalSettings } from "../../../services";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  setExpandedKeys,
  setMenuCollapsed,
} from "../../../store/features/sidebar";
import { setPinned } from "../../../store/features/pinned";
import { MyTooltip, OverflowTooltip } from "../../atoms";
import {
  useHyperlinkActions,
  useNotification,
} from "../../../utils/functions/customHooks";
import i18next from "i18next";
import { setRefreshBreadcrumbs } from "../../../store/features";
import { getAllNodes, getHierarchyDetails } from "../../../services/node";
import { ResizableDiv } from "../ResizableDiv";
import { getParentIds } from "../../../utils/functions/getParentIds";
import { setTrashCollapsed } from "../../../store/features/trashcan";

interface Props {
  onClose?: () => void;
}

// const mockupMenus = ["message", "grades", "comments", "history"];

const STATE_KEY = "home-sidebar";

const HomeSidebar = ({ onClose }: Props) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const theme = useTheme();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const { contextHolder, showSuccessNotification } = useNotification();

  const [selected, setSelected] = useState("");
  const [items, setItems] = useState([]);
  const [sidebarWidth, setSidebarWidth] = useState(260);

  const { menuCollapsed, mask, attributeMask } = useSelector(
    (state: RootState) => state.sidebar
  );
  const { pinned } = useSelector((state: RootState) => state.pinned);

  const allTemplates = useSelector(
    (root: RootState) => root.templatesStore.templates
  );
  const trashCollapsed = useSelector(
    (root: RootState) => root.trash.trashCollapsed
  );
  const { profileId, role } = useSelector((root: RootState) => root.auth);

  const { handleTrashHyperlinkClick } = useHyperlinkActions();

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    const width = localStorage.getItem(STATE_KEY);
    if (width) setSidebarWidth(Number(width));
  }, []);

  useEffect(() => {
    if (
      localSettingsData &&
      localSettingsData?.body?.length > 0 &&
      localSettingsData?.body[0]?.value?.pinned
    ) {
      dispatch(setPinned(localSettingsData.body[0].value.pinned));
    }
  }, [localSettingsData]);

  useEffect(() => {
    const selectedKey = location.pathname.split("/")[1];
    if (selectedKey.length > 0) {
      setSelected(selectedKey);
    } else {
      setSelected("home");
    }
  }, [location.pathname]);

  const handleChange = ({ key }) => {
    if (key === "more-favs") {
      return;
    }

    if (!key.startsWith("/details")) {
      if (key === "trashcan") {
        localStorage.setItem("trash-collapsed", "1");
        dispatch(setTrashCollapsed(!trashCollapsed));
      } else if (key === "home") {
        navigate(`/`);
      } else {
        navigate(`/${key}`);
      }
      onClose && onClose();
    }
  };

  const { data: trashcanData, isRefetching: isTrashRefetching } = useQuery<
    ITreeData[],
    Error
  >(
    [GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID],
    () => getAllNodes(TRASHCAN_PARENT_NODE_ID),
    {
      enabled: profileId !== NO_USER_ASSOCIATED,
    }
  );

  // FOR COUNTERS

  // const { data: commentsData, isRefetching: isCommentsRefetching } = useQuery(
  //   GET_ALL_COMMENTS,
  //   () => getAllComments(userInfo?.id),
  //   {
  //     enabled: profileId !== NO_USER_ASSOCIATED,
  //   }
  // );

  // const getCount = (key) => {
  //   switch (key) {
  //     case "favorite":
  //       return pinned.length || 0;
  //     case "message":
  //       return 5;
  //     case "history":
  //       return 50;
  //     case "trashcan":
  //       return trashcanData?.length || 0;
  //     case "comments":
  //       return commentsData?.length || 0;
  //     default:
  //       return 0;
  //   }
  // };

  const getItems = () => {
    const homeItems = [...homeSidebarItems];
    const person_role = profileId === NO_USER_ASSOCIATED ? "no-person" : role;
    const newItems = [];
    homeItems.forEach((item) => {
      if (!item?.allowedroles?.includes(person_role)) return;
      if (trashcanData?.length === 0 && item.key === "trashcan") {
        dispatch(setTrashCollapsed(true));
        localStorage.setItem("trash-collapsed", "0");
        return;
      }

      if (item.key === selected) {
        if (item.key === "favorite" && pinned.length <= 4) {
          return;
        }

        newItems.push({
          ...item,
          icon: getActiveIcon(item.key),
          label: (
            <OverflowTooltip
              className="title"
              title={t(item.label)}
              id={`${item.key}-menu-item`}
            >
              {t(item.label)}{" "}
              {/* {item.key !== "settings" && (
                <span
                  className="count"
                  style={{ color: item.key === "trashcan" ? "red" : "" }}
                >
                  {menuCollapsed
                    ? `(${getCount(item.key)})`
                    : getCount(item.key)}
                </span>
              )} */}
            </OverflowTooltip>
          ),
        });
      } else {
        if (item.key === "favorite" && pinned.length <= 4) {
          return;
        }

        newItems.push({
          ...item,
          icon:
            !trashCollapsed && item.key === "trashcan" ? (
              <span style={{ color: "red" }}>
                <DeleteFilled />
              </span>
            ) : (
              item.icon
            ),
          label: (
            <OverflowTooltip
              className="title"
              title={t(item.label)}
              style={
                !trashCollapsed && item.key === "trashcan" && !menuCollapsed
                  ? {
                      color: "red",
                    }
                  : {}
              }
            >
              {t(item.label)}

              {/* For counters */}
              {/* {item.key !== "settings" && (
                <span
                  className="count"
                  style={{ color: item.key === "trashcan" ? "red" : "" }}
                >
                  {menuCollapsed
                    ? `(${getCount(item.key)})`
                    : getCount(item.key)}
                </span>
              )} */}
              {/* {mockupMenus.includes(item.key) && (
                <span className="mockup">{t("(mockup)")}</span>
              )} */}
            </OverflowTooltip>
          ),
        });
      }
    });
    const favItems = [];

    pinned.slice(0, 4).forEach((items) => {
      favItems.push({
        key: `/details/${items.id}`,
        label: menuCollapsed ? (
          items.name
        ) : (
          <div className="fav-items-wrapper">
            <OverflowTooltip title={items.name}>
              <Link
                to={
                  items?.inTrash
                    ? null
                    : `/details/${items.parentId}?nodeId=${items.id}`
                }
                onClick={() => {
                  if (items?.inTrash) {
                    handleTrashHyperlinkClick(items.id);
                  } else handleFavoritesClick(items.id);
                }}
              >
                {items.name}
              </Link>
            </OverflowTooltip>
            <Tooltip title={t("Remove from favorite")} placement="topLeft">
              <PushpinFilled
                className="remove-from-pinned"
                onClick={(e) => {
                  e.stopPropagation();
                  const newFavorites = pinned?.filter(
                    (fav) => fav?.id !== items.id
                  );

                  mutation.mutateAsync({
                    value: {
                      ...(localSettingsData?.body
                        ? localSettingsData?.body[0]?.value || {}
                        : {}),
                      pinned: newFavorites,
                    },
                  });
                  dispatch(setPinned([...newFavorites]));
                  showSuccessNotification(
                    "Selected items removed from pinned successfully!"
                  );
                }}
              />
            </Tooltip>
          </div>
        ),
        className: "favorite-items",
        icon: (
          <div>
            <Link
              to={
                items?.inTrash
                  ? null
                  : `/details/${items.parentId}?nodeId=${items.id}`
              }
              onClick={() => {
                if (items?.inTrash) {
                  handleTrashHyperlinkClick(items.id);
                } else handleFavoritesClick(items.id);
              }}
            >
              {getTemplateIcon(allTemplates, items.templateId)}
            </Link>
          </div>
        ),
      });
    });

    return [
      {
        key: "home",
        label: <OverflowTooltip title={t("Home")}>{t("Home")}</OverflowTooltip>,
        icon: <HomeIcon className="home" />,
      },
      pinned.length > 0 && { type: "divider" },
      pinned.length > 4 && {
        label: (
          <div
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/pinned`);
            }}
          >
            <OverflowTooltip
              className="title"
              title={t("More Favorites") + " (" + pinned.length + ")"}
            >
              {t("More Favorites")}{" "}
              <span className="count">
                {" "}
                {menuCollapsed ? `(${pinned.length || 0})` : pinned.length || 0}
              </span>
            </OverflowTooltip>
          </div>
        ),
        key: "more-favs",
        icon: (
          <Link to="/pinned">
            <PushpinFilled />
          </Link>
        ),
      },
      ...favItems,
      pinned.length > 0 && { type: "divider" },
      ...newItems,
    ];
  };

  useEffect(() => {
    // if (!trashcanData ) return;
    setItems([...getItems()]);
  }, [
    pinned,
    allTemplates,
    selected,
    i18next.language,
    menuCollapsed,
    trashCollapsed,
    trashcanData,
    isTrashRefetching,
  ]);

  const mutation = useMutation(saveLocalSettings);

  const handleFavoritesClick = async (id: number) => {
    const parentPath = await getHierarchyDetails(id);
    const expandedKeys = getParentIds(parentPath.path);
    dispatch(setRefreshBreadcrumbs(true));
    dispatch(setExpandedKeys(expandedKeys));
  };

  const {
    movingMask,
    workingVersion: workingVersionActive,
    bottomDrawer: bottomNavigationMask,
  } = useSelector((state: RootState) => state.mask);

  return (
    <Layout.Sider
      trigger={null}
      collapsible
      collapsed={menuCollapsed}
      collapsedWidth={60}
      width={sidebarWidth}
    >
      <Wrapper
        theme={theme}
        className={`wrapper ${menuCollapsed ? "hide-indicator" : ""}`}
      >
        {contextHolder}
        <ResizableDiv
          resize="right"
          onResize={(size: any) => {
            if (size.x > 80) setSidebarWidth(size.x);
          }}
          maxWidth={"100%"}
          minWidth={80}
          saveWidthToLocalStorage
          stateKey={STATE_KEY}
          style={{
            display: menuCollapsed ? "contents" : "block",
            transition: "display 0.5s ease-in",
          }}
        >
          {menuCollapsed ? (
            <MyTooltip title="Expand">
              <MenuUnfoldOutlined
                style={{ zIndex: 1 }}
                className="collapsed-menu"
                id="expand-tour-item"
                onClick={() => {
                  localStorage.setItem("menu-collapsed", "0");
                  dispatch(setMenuCollapsed(false));
                }}
              />
            </MyTooltip>
          ) : (
            <Tooltip title={t("Collapse")} zIndex={2000000}>
              <MenuFoldOutlined
                id="expand-tour-item"
                style={{ zIndex: 1 }}
                onClick={() => {
                  localStorage.setItem("menu-collapsed", "1");
                  dispatch(setMenuCollapsed(true));
                }}
              />
            </Tooltip>
          )}
          <div style={{ position: "relative", height: "100%" }}>
            {(mask ||
              attributeMask ||
              bottomNavigationMask ||
              workingVersionActive ||
              movingMask) && <Mask className="mask" />}

            <Menu
              mode="inline"
              selectedKeys={[selected]}
              theme="light"
              onClick={handleChange}
              items={[...(items as any)]}
              defaultOpenKeys={menuCollapsed ? [] : ["more-favs"]}
            />
          </div>
        </ResizableDiv>
      </Wrapper>
    </Layout.Sider>
  );
};

export { HomeSidebar };

const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 2;
`;
const Wrapper = styled.div<{ theme: any }>`
  display: contents;

  & .count {
    position: absolute;
    right: 0;
    font-size: 11px;
    line-height: 15px;
    top: 50%;
    transform: translate(0px, -50%);
    background: #edf2f8;
    min-width: 23px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  & a.ant-menu-item-icon {
    line-height: 1 !important;
    & span {
      line-height: 1 !important;
    }
  }
  & .anticon-link {
    & svg {
      width: 14px;
    }
  }

  & ul {
    overflow: auto;
  }

  & .ant-menu-submenu-title {
    margin-left: 0px !important;
    margin-right: 0px !important;
    width: 100%;
  }
  & .no-resize .indicator {
    display: none;
  }

  & .ant-menu-item-divider {
    border-top-width: 1.5px;
    border-color: #e1e1e1;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  & > div {
    height: 100% !important;
  }

  & .fav-items-wrapper {
    display: flex;
    justify-content: space-between;

    & a {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  & a {
    color: ${({ theme }) => theme.colorPrimary};
  }

  & > div > span {
    position: absolute;
    right: 10px;
    color: ${({ theme }) => theme.colorPrimary};
    top: 20px;
    font-size: 17px;
  }

  & .ant-menu-root > li {
    width: auto !important;
    margin-left: 10px !important;
    margin-right: 14px !important;
  }

  & img {
    object-fit: contain;
    width: 18px;
    height: 18px;
  }

  & .collapsed-menu {
    left: 20px;
    width: fit-content;
  }
  & .ant-menu-inline-collapsed {
    width: 60px !important;
  }
  & .ant-menu-inline .ant-menu-submenu {
    background-color: ${({ theme }) => theme.bgAttributes};
  }

  & .ant-menu-root {
    border-right: 1px solid #efefef;
    background: #fff;
    padding-top: 60px;
    height: 100%;
    & .ant-menu-item-selected {
      background-color: ${({ theme }) => theme.bgAttributes} !important;
      color: ${({ theme }) => theme.colorPrimary} !important;
      & .home path {
        fill: ${({ theme }) => theme.colorPrimary} !important;
      }

      &:hover {
        color: ${({ theme }) => theme.colorPrimary} !important;
        background-color: #fff !important;
      }
    }
    & .ant-menu-submenu {
      color: ${({ theme }) => theme.colorPrimary};
    }

    & > .ant-menu-item,
    .ant-menu-submenu-title {
      color: ${({ theme }) => theme.colorPrimary};
      padding-left: 16px !important;
      height: 44px !important;
      line-height: 44px !important;
      display: flex;
      align-items: center;
      & .home path {
        stroke: ${({ theme }) => theme.colorPrimary};
      }

      &:hover {
        background-color: rgba(191, 195, 217, 0.17) !important;
        color: ${({ theme }) => theme.colorPrimary} !important;
      }

      & .fav-items-wrapper .anticon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translate(0px, -50%);
        & svg:not(.anticon-link) {
          min-width: 15px !important;
          height: 18px;
        }
      }

      & span:not(.anticon-link) > svg,
      .home {
        min-width: 18px !important;
        height: 20px;
      }
    }
    & .favorite-items {
      height: 32px !important;
    }
  }

  & .ant-menu-sub {
    background: none !important;
    & > li {
      color: ${({ theme }) => theme.colorPrimary};
      display: flex;
      align-items: center;
      & path {
        stroke: ${({ theme }) => theme.colorPrimary};
      }

      &:hover {
        background-color: rgba(191, 195, 217, 0.17) !important;
        color: ${({ theme }) => theme.colorPrimary} !important;
      }
    }
  }

  & .ant-menu-inline-collapsed .ant-menu-item {
    margin-left: 6px !important;
    padding-left: 11px !important;
  }

  & .ant-menu-inline-collapsed .ant-menu-submenu-title {
    padding-left: 11px !important;
  }
`;
