import { styled } from "@linaria/react";
import { <PERSON><PERSON>, Modal, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { getAllNodes, restoreTemplates } from "../../../../services/node";
import { useEffect, useRef } from "react";
import {
  GET_ALL_TEMPLATES_KEY,
  GET_CHILDRENS,
  getAttributeIcon,
} from "../../../../constants";
import { captureException } from "@sentry/react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";

const RestoreNodeTemplateModal = ({
  onClose,
  isOpen,
  label,
  onDeleteClick,
  afterDelete,
  isMultiple,
  templateId,
}: Props) => {
  const { t } = useTranslation();
  const ref = useRef<any>();
  const { selected } = useSelector((state: RootState) => state.sidebar);
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );
  const queryClient = useQueryClient();

  useEffect(() => {
    ref?.current?.focus();
  }, [isOpen]);

  const invalidateParents = (parentIds: string[]) => {
    parentIds.forEach(async (parentId) => {
      await getAllNodes(parentId).then((childrens) => {
        queryClient.setQueryData(
          [GET_CHILDRENS, parentId.toString()],
          childrens
        );
      });
    });
  };

  const mutation = useMutation(restoreTemplates, {
    onSuccess: (restoredIds: number[]) => {
      afterDelete(restoredIds);
      queryClient.invalidateQueries(GET_ALL_TEMPLATES_KEY);

      notification.success({
        message: t("Success!"),
        description: t("Template activated successfully!"),
      });

      const toInvalidateParents = [];
      selected?.info?.forEach((item) => {
        toInvalidateParents.push(item.parentId);
      });

      invalidateParents(toInvalidateParents);

      onClose();
    },
    onError: (e: any) => {
      notification.error({
        message: t("Error Occurred!"),
        description: e?.data?.error || "Please try again after sometime",
      });
      captureException(e?.data?.error);
    },
  });

  const handleDelete = () => {
    if (onDeleteClick) {
      onDeleteClick();
      onClose();
    } else {
      mutation.mutate(selected.keys);
    }
  };

  let templateIcon = null;
  if (!isMultiple) {
    templateIcon = templatesData[Number(templateId)]?.icon || "_30_folder";
  }

  const getModalTitle = () => {
    if (isMultiple) {
      return `${t("Activate selected")}?`;
    }
    return `${t("Activate")} ${label}?`;
  };

  const getModelBody = () => {
    if (isMultiple)
      return t("Are you sure you want to activate selected templates?");

    return t("Are you sure to activate this template?");
  };

  const getButtonLabel = () => {
    if (isMultiple) return t("Activate selected");
    return t("Activate");
  };

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      centered
      className="export-modal"
      title={getModalTitle()}
      width="50%"
    >
      <Wrapper>
        <h5>{getModelBody()}</h5>
        {!isMultiple && (
          <p>
            {getAttributeIcon(templateIcon)} {label}
          </p>
        )}
        <Button
          onClick={handleDelete}
          type="primary"
          ref={ref}
          loading={mutation.isLoading}
        >
          {getButtonLabel()}
        </Button>
      </Wrapper>
    </Modal>
  );
};

export { RestoreNodeTemplateModal };

interface Props {
  onClose: () => void;
  isOpen: boolean;
  id: string;
  label: string;
  onDeleteClick?: () => void;
  afterDelete?: (id) => void;
  isMultiple?: boolean;
  selectedKeys?: number[];
  hasAssetTypeNode?: boolean;
  templateId?: number;
}

const Wrapper = styled.form`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;

  & img {
    object-fit: contain;
  }

  & h5 {
    font-size: 13px;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 6px;

    & span {
      color: #949494;
      font-size: 12px;
      font-style: italic;
    }
  }

  & p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4277a2;

    & svg {
      width: 20px;
      height: 20px;
    }
  }
  & button {
    margin-left: auto;
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }
`;
