import { useEffect, useState } from "react";
import { DetailsContainer, MyTable } from "../../components";
import { <PERSON><PERSON>, Divider, Dropdown, Flex } from "antd";
import { MyTooltip } from "../../components/atoms/MyTooltip";
import { GET_LOCAL_SETTINGS_KEY, NODES_MENU_ITEMS } from "../../constants";
import { FullscreenOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { styled } from "@linaria/react";
import { Link } from "react-router-dom";
import { getHierarchyDetails } from "../../services/node";
import {
  setExpandedKeys,
  setHomeSectionMask,
  setRefreshBreadcrumbs,
} from "../../store/features";
import { getParentIds } from "../../utils/functions/getParentIds";
import { useMutation, useQueryClient } from "react-query";
import { ILocalSettings } from "../../interfaces";
import dayjs from "dayjs";
import { RootState } from "../../store";
import { useTranslation } from "react-i18next";
import { saveLocalSettings } from "../../services";
import {
  useHyperlinkActions,
  useNotification,
  useTemplateActions,
} from "../../utils/functions/customHooks";

const PinnedTable = ({ listeners }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();
  const { getTemplateIcon, getTemplateName } = useTemplateActions();
  const { handleTrashHyperlinkClick } = useHyperlinkActions();

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );

  const handleFavoritesClick = async (id: number) => {
    const parentPath = await getHierarchyDetails(id);
    dispatch(setRefreshBreadcrumbs(true));
    const parentIds = getParentIds(parentPath.path);
    dispatch(setExpandedKeys(parentIds));
  };
  const COLUMNS = [
    {
      headerName: "Name",
      field: "name",
      flex: 1,
      minWidth: 100,
      cellRenderer: ({ data }) => {
        return (
          <Dropdown
            menu={{
              items: NODES_MENU_ITEMS,
              onClick: (e) =>
                handleNodeClick(e.key, data.id, data.name, data.parentId),
            }}
            trigger={["contextMenu"]}
          >
            <NameWrapper
              to={
                data?.inTrash
                  ? null
                  : `/details/${data.parentId}?nodeId=${data.id}`
              }
              onClick={() => {
                if (data?.inTrash) {
                  handleTrashHyperlinkClick(data.id);
                } else handleFavoritesClick(data.id);
              }}
            >
              {data.name}
            </NameWrapper>
          </Dropdown>
        );
      },
    },
    {
      headerName: "Template",
      field: "templateName",
      width: 150,
      cellRenderer: (params) => {
        const record = params?.data;

        return (
          <p className="template-container">
            {getTemplateIcon(record?.templateId)}
            {record.templateName}
          </p>
        );
      },
    },
    {
      headerName: "Date",
      field: "date",
      isDate: true,
      flex: 1,
      cellRenderer: ({ data }) => {
        return dayjs(data?.date).format("YYYY/MM/DD HH:mm");
      },
    },
  ];

  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinnedColumns, setPinnedColumns] = useState([]);
  const [pinnedItems, setPinnedItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [allColumnsRequest, setAllColumnsRequest] = useState([]);
  const [columns, setColumns] = useState(COLUMNS);
  const [triggerChange, setTriggerChange] = useState(null);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const pinned = useSelector((state: RootState) => state.pinned.pinned);

  const handleNodeClick = async (key, id, name, parentId) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const baseUrl =
          import.meta.env.VITE_APP_BASE_URL === "/"
            ? ""
            : import.meta.env.VITE_APP_BASE_URL;

        window.open(
          `${window.origin}${baseUrl}/details/${parentId}?nodeId=${id}`
        );
      }
    }
  };

  const detectChange = () => {
    dispatch(setHomeSectionMask("pinned"));
  };

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    if (!localSettingsData && !templatesData) {
      return;
    }

    if (
      localSettingsData?.body[0]?.value?.favoritesTable?.columns &&
      localSettingsData?.body[0]?.value?.favoritesTable?.columns?.length > 0
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.favoritesTable?.pinned || [];
      const sort =
        localSettingsData?.body[0]?.value?.favoritesTable?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.favoritesTable.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );
      setAllColumnsRequest(
        localSettingsData?.body[0]?.value?.favoritesTable?.columns
      );
      setPinnedColumns(
        localSettingsData?.body[0]?.value?.favoritesTable?.pinned
      );
      setSort(localSettingsData?.body[0]?.value?.favoritesTable?.sort);
      setFilters(localSettingsData?.body[0]?.value?.favoritesTable?.filters);
      setColumns([...allColumns]);
    } else {
      setColumns(COLUMNS);
      setAllColumnsRequest(COLUMNS?.map((col) => col.field));
    }

    setLoading(false);
  }, [localSettingsData, templatesData, triggerChange]);

  useEffect(() => {
    setPinnedItems(
      pinned?.map((item) => {
        return {
          ...item,
          date: new Date(item.date),
          templateId: Number(item?.templateId),
          templateName: getTemplateName(item?.templateId),
        };
      })
    );
  }, [pinned, localSettingsData]);

  const handleCancel = () => {
    setTriggerChange((prev) => prev + 1);
    setTimeout(() => {
      dispatch(setHomeSectionMask(null));
    }, 200);
  };

  const handleSave = async () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        favoritesTable: {
          columns: allColumnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinnedColumns,
        },
        pinned: [...pinned],
      },
    };
    await mutation.mutateAsync(request);

    queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
    showSuccessNotification("Changes published successfully!");
    dispatch(setHomeSectionMask(null));
  };

  const mutation = useMutation(saveLocalSettings, {
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setHomeSectionMask(null));
    },
  });

  return (
    <>
      {contextHolder}
      <Flex className="header-wrapper" justify="space-between" align="center">
        <h4 {...listeners}>{t("PINNED")}</h4>
        {pinned?.length > 4 && !homeSectionMask && (
          <div className="actions">
            <Link to="/pinned" className="view-all">
              <MyTooltip title="View all">
                <FullscreenOutlined />
              </MyTooltip>
            </Link>
          </div>
        )}
        {homeSectionMask === "pinned" && (
          <Flex gap={10}>
            <Button
              className="breadcrumb-button cancel cancel-button"
              onClick={handleCancel}
            >
              {t("Cancel")}
            </Button>
            <Button
              className="breadcrumb-button save-button"
              onClick={handleSave}
              loading={mutation.isLoading}
            >
              {t("Save")}
            </Button>
          </Flex>
        )}
      </Flex>
      <Divider />
      <article style={{ maxHeight: 400, overflow: "auto", marginBottom: 20 }}>
        <MyTable
          resetTrigger={triggerChange}
          loading={loading}
          height={"250px"}
          columns={columns}
          data={pinnedItems}
          setColumnsRequest={setAllColumnsRequest}
          detectChange={detectChange}
          excelFileName="favorites"
          setSort={setSort}
          setPinned={setPinnedColumns}
          setFilters={setFilters}
          initialFilters={
            localSettingsData?.body[0]?.value?.favoritesTable?.filters || {}
          }
        />
      </article>

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </>
  );
};

export { PinnedTable };

const NameWrapper = styled(Link)`
  display: flex;
  gap: 8px;
  text-align: left;
  font-size: 13px;
  align-items: center;
  width: fit-content;
  color: var(--color-text);
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }

  & svg {
    min-width: 16px;
    height: 16px;
  }

  & img {
    object-fit: contain;
    height: 16px;
  }
`;
