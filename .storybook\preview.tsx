import type { Preview } from "@storybook/react";
import "../src/index.css";
import { DecoratorFn } from "@storybook/react";
import { ReduxDecorator } from "../src/stories/reduxDecorator";

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
};

export const decorators: DecoratorFn[] = [ReduxDecorator];

export default preview;
