import { styled } from "@linaria/react";
import { useEffect, useRef, useState } from "react";
import { MyTable } from "../../../organisms";
import { useDispatch, useSelector } from "react-redux";
import {
  useNotification,
  usePermissions,
} from "../../../../utils/functions/customHooks";
import { setBottomDrawerMask } from "../../../../store/features";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  GET_COMMENTS,
  GET_COUNTERS,
  GET_LOCAL_SETTINGS_KEY,
  COMMENT_TEMPLATE_ID,
  COMMENT_AUTHOR_ID,
  GET_NODE_ATTRIBUTES_DETAILS,
  COMMENT_RATING_ID,
  GET_ALL_COMMENTS,
} from "../../../../constants";
import { getComments, saveLocalSettings } from "../../../../services";
import { RootState } from "../../../../store";
import { debounce } from "lodash";
import { Button, Empty, Popconfirm, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { deleteNodeService } from "../../../../services/node";
import { AddComment, SimpleAttributeValue } from "../../../atoms";
import { ILocalSettings, INodeDetails } from "../../../../interfaces";
import { setTrashcanDrawerMask } from "../../../../store/features/trashcan";

type Props = {
  addOpen?: boolean;
  onClose?: any;
  fromTrashcan: boolean;
  displaySaveCancel: boolean;
  id: string;
};

const CommentTableContainer = ({
  id,
  displaySaveCancel,
  fromTrashcan,
}: Props) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const parentRef = useRef(null);

  const [rows, setRows] = useState([]);
  const [height, setHeight] = useState(0);
  const [permissions, setPermissions] = useState([]);
  const [columns, setColumns] = useState([]);
  const [displayedColumns, setDisplayedColumns] = useState([]);
  const [addComment, setAddComment] = useState(false);

  const { getPermissions } = usePermissions();
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const detectChange = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(true));
    } else {
      dispatch(setBottomDrawerMask(true));
    }
  };

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  // get all comments for the node
  const { data, isLoading, refetch, isError } = useQuery(
    [GET_COMMENTS, id],
    () => getComments(id)
  );

  useEffect(() => {
    const bodyData = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      id?.toString(),
    ]) as INodeDetails;
    if (bodyData) {
      setPermissions(getPermissions(bodyData?.permissionsId));
    }
  }, [id]);

  // height calculation
  useEffect(() => {
    const parent = parentRef.current;
    if (!parent) return;

    const updateHeight = debounce((entries) => {
      for (const entry of entries) {
        setHeight(entry.contentRect.height);
      }
    }, 100);

    const observer = new ResizeObserver(updateHeight);
    observer.observe(parent);

    return () => {
      observer.disconnect();
    };
  }, []);

  // deleting the comment
  const deleteMutation = useMutation(deleteNodeService, {
    onError: () => {
      showErrorNotification("Error in deletion!");
    },
  });

  useEffect(() => {
    if (!id) {
      setColumns([]);
      setRows([]);
      return;
    }

    // generating dynamic rows and columns
    const _columns = [];

    const attributeTemplates =
      templatesData[COMMENT_TEMPLATE_ID]?.attributeTemplates || [];

    if (data?.length > 0) {
      attributeTemplates?.forEach((attribute) => {
        _columns.push({
          headerName: attribute.name,
          field: attribute.name,
          minWidth: attribute?.id === COMMENT_RATING_ID ? 130 : 250,
          flex: 1,
          cellRenderer: ({ data }) => {
            const ratingValue = Object.values(data[attribute.name])?.[0];
            const isEmptyRating = typeof ratingValue === 'string' && ratingValue.trim() === "-";

            if (attribute?.id === COMMENT_RATING_ID && !isEmptyRating) {
              return (
                <div
                  className={`comment-rating ${
                    Object.values(data[attribute.name])[0] === "Negatywna"
                      ? "negative-rating"
                      : "positive-rating"
                  }`}
                />
              );
            }

            if (attribute?.id === COMMENT_AUTHOR_ID) {
              const authorValue = data[attribute.name];
              if (Array.isArray(authorValue)) return authorValue[0]?.name || "-";
              if (typeof authorValue === 'object') return Object.values(authorValue)[0] || "-";
              return authorValue || "-";
            }

            return (
              <SimpleAttributeValue
                attributeType={attribute.type}
                attributeValue={data[attribute.name] || "-"}
              />
            );
          },
        });
      });
    }

    _columns.push({
      field: "actions",
      headerName: "",
      isAction: true,
      width: 70,
      cellRenderer: ({ data }) => {
        return (
          <Actions>
            <Popconfirm
              title={`${t("Delete")}?`}
              description={t("Are you sure to delete this comment?")}
              onConfirm={async () => {
                // This API is being called twice once to move to trash and again to permanently delete.
                await deleteMutation.mutateAsync({ id: data?.id });
                await deleteMutation.mutateAsync({ id: data?.id });
                queryClient.invalidateQueries([GET_COUNTERS, id]);
                queryClient.invalidateQueries(GET_ALL_COMMENTS);
                refetch();
                showSuccessNotification("Comment deleted successfully!");
              }}
              okText={t("Yes")}
              cancelText={t("No")}
            >
              <Tooltip title={t("Delete")}>
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </Actions>
        );
      },
    });
    setColumns(_columns);

    // generating rows
    const rows = [];
    data?.forEach((comment) => {
      const row = {};

      attributeTemplates?.forEach((attribute) => {
        row["id"] = comment?.id;
        row[attribute?.name] =
          comment?.body?.find((attr) => attr.id === attribute.id)?.value ||
          "-";
      });
      rows.push(row);
    });

    setRows([...rows]);
  }, [data]);

  // get local settings for comments drawer
  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      columns?.length > 0 &&
      localSettingsData?.body[0]?.value?.commentsDrawer &&
      localSettingsData?.body[0]?.value?.commentsDrawer?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.commentsDrawer?.pinned || [];
      const sort =
        localSettingsData?.body[0]?.value?.commentsDrawer?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.commentsDrawer.columns?.forEach(
        (column) => {
          if (!column) {
            return;
          }
          const index = columns.findIndex((item) => item.field === column);
          allColumns.push({
            ...columns[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );
      setDisplayedColumns(allColumns);
    } else {
      setDisplayedColumns(columns);
    }
  }, [localSettingsData, columns]);

  const handleCancel = () => {
    setTimeout(() => {
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
    }, 200);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setBottomDrawerMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
    },
  });

  // save table details to local settings
  const handleSave = (newColumns: string[], filters, sort, pinned) => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        commentsDrawer: {
          columns: newColumns,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  return (
    <Wrapper
      ref={parentRef}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {contextHolder}

      {data?.length === 0 ? (
        <div className="empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t("No comments")}
          />
          {permissions.includes("EDIT") && (
            <Button onClick={() => setAddComment(true)} type="primary">
              {t("Add")} {templatesData[COMMENT_TEMPLATE_ID]?.name}
            </Button>
          )}
        </div>
      ) : (
        <MyTable
          excelFileName="comments"
          isError={isError}
          columns={displayedColumns}
          data={rows}
          height={`${height - 50}px`}
          emptyMessage="No comments"
          loading={isLoading}
          detectChange={detectChange}
          displaySaveCancel={displaySaveCancel}
          onCancelClick={handleCancel}
          saveLoading={mutation.isLoading}
          onSaveClick={handleSave}
          fullHeight
          initialFilters={
            localSettingsData?.body[0]?.value?.commentsDrawer?.filters || {}
          }
          extra={
            permissions.includes("ATTACH") && (
              <Button
                onClick={() => setAddComment(true)}
                type="primary"
                className="add-comment"
              >
                <PlusOutlined />
                {t("Add")} {templatesData[COMMENT_TEMPLATE_ID]?.name}
              </Button>
            )
          }
        />
      )}

      {addComment && (
        <AddComment
          onSuccess={() => {
            showSuccessNotification("Comment added successfully!");
          }}
          visible={addComment}
          onHide={() => setAddComment(false)}
        />
      )}
    </Wrapper>
  );
};

export { CommentTableContainer };

const Actions = styled.div`
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;

  & .anticon-edit {
    color: var(--color-text);
  }

  & .anticon-delete {
    color: red;
  }
`;

const Wrapper = styled.div`
  overflow: hidden;
  padding: 10px;
  height: 100%;
  display: flex;
  width: 100%;
  position: relative;

  & .add-comment {
    box-shadow: none;
    padding: 19px 10px;
    font-size: 13px;
    border-radius: 16px;
  }
  & .empty {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;

    & .ant-empty {
      margin-bottom: 16px;
    }

    & button {
      border: 1px solid;
      color: #084375;
      background: #fff;
      box-shadow: none;
      font-size: 13px;
    }
  }

  & .my-table {
    width: 100%;
  }

  & .ant-avatar {
    border-radius: 10px;
  }
  & .anticon-export {
    font-size: 17px;
    margin-left: 10px;
  }

  & .ant-float-btn {
    right: 10px;
  }

  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }
`;
