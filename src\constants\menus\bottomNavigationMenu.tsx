import { Dropdown } from "antd";
import {
  <PERSON>set<PERSON>ontainer,
  At<PERSON>chmentContainer,
  BottomHistoryContainer,
  BottomRelationContainer,
  CommentTableContainer,
  LogsContainer,
  PermissionContainer,
  SuspectsContainer,
  TestExecutionContainer,
  TestPerformanceChart,
  WorkingVersion,
} from "../../components";
import { PlusOutlined, WarningFilled } from "@ant-design/icons";
import { GraphContainer } from "../../components/molecules/BottomDrawer/GraphContainer";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../store";

export const useGenerateBottomNavigationMenu = () => {
  const { t } = useTranslation();

  const navigationItems = useSelector(
    (root: RootState) => root.navigation.navigationItems
  );

  const handleOpenInNewTab = (id) => {
    const baseUrl =
      import.meta.env.VITE_APP_BASE_URL === "/"
        ? ""
        : import.meta.env.VITE_APP_BASE_URL;
    window.open(`${baseUrl}/details/graph/${id}`);
  };

  const getItem = (
    key: string,
    countersData,
    id,
    noGraph,
    fromTrashcan: boolean,
    displaySaveCancel: boolean,
    isOpen: boolean
  ) => {
    switch (key) {
      case "permissions":
        return {
          label: (
            <p>
              {t("Permissions")}{" "}
              {/* <span className="counter">{countersData?.ASSETS || 0}</span> */}
            </p>
          ),
          key: "permissions",
          children: isOpen ? (
            <PermissionContainer
              id={id}
              displaySaveCancel={displaySaveCancel}
              fromTrashcan={fromTrashcan}
            />
          ) : null,
        };

      case "assets":
        return {
          label: (
            <p>
              {t("Assets")}{" "}
              <span className="counter">{countersData?.ASSETS || 0}</span>
            </p>
          ),
          key: "assets",
          children: isOpen ? (
            <AssetContainer
              id={id}
              displaySaveCancel={displaySaveCancel}
              fromTrashcan={fromTrashcan}
            />
          ) : null,
        };

      case "relation":
        return {
          label: (
            <p>
              {t("Relations")}{" "}
              <span className="counter">{countersData?.RELATIONS || 0}</span>
            </p>
          ),
          key: "relation",
          children: isOpen ? (
            <BottomRelationContainer
              id={id}
              fromTrashcan={fromTrashcan}
              displaySaveCancel={displaySaveCancel}
            />
          ) : null,
        };
      case "working-version":
        return {
          label: (
            <p>
              {t("Draft version")} <span className="counter">{1}</span>
            </p>
          ),
          key: "working-version",
          children: isOpen ? <WorkingVersion /> : null,
        };

      // case "charts":
      //   return {
      //     label: <p>{t("Charts")}</p>,
      //     key: "charts",
      //     children: <BottomChartContainer />,
      //   };

      case "history":
        return {
          label: (
            <p>
              {t("History")}{" "}
              <span className="counter">{countersData?.HISTORY || 0}</span>
            </p>
          ),
          key: "history",
          children: isOpen ? (
            <BottomHistoryContainer
              id={id}
              displaySaveCancel={displaySaveCancel}
              fromTrashcan={fromTrashcan}
            />
          ) : null,
        };

      case "logs":
        return {
          label: (
            <p>
              {t("Log")}{" "}
              <span className="counter">{countersData?.LOGS || 0}</span>
            </p>
          ),
          key: "logs",
          children: isOpen ? (
            <LogsContainer
              id={id}
              displaySaveCancel={displaySaveCancel}
              fromTrashcan={fromTrashcan}
            />
          ) : null,
        };

      case "suspects":
        return {
          label: (
            <p style={{ color: "red", fontWeight: 500 }}>
              <WarningFilled style={{ marginRight: 5 }} />
              {t("Suspects")}{" "}
              <span className="counter suspect-counter">
                {countersData?.SUSPECTED || 0}
              </span>
            </p>
          ),
          key: "suspects",
          children: isOpen ? <SuspectsContainer id={id} /> : null,
        };

      case "attachments":
        return {
          label: (
            <p>
              {t("Attachments")}{" "}
              <span className="counter">{countersData?.ATTACHMENTS || 0}</span>
            </p>
          ),
          key: "attachments",
          children: isOpen ? (
            <AttachmentContainer
              id={id}
              displaySaveCancel={displaySaveCancel}
              fromTrashcan={fromTrashcan}
            />
          ) : null,
        };

      case "graph":
        if (noGraph) {
          return null;
        }

        return {
          label: (
            <Dropdown
              trigger={["contextMenu"]}
              menu={{
                items: [
                  {
                    key: "open-in-new-tab",
                    label: t("Open in new tab"),
                  },
                ],
                onClick: () => handleOpenInNewTab(id),
              }}
            >
              <p>{t("Graph")}</p>
            </Dropdown>
          ),
          key: "graph",
          children: isOpen ? (
            <GraphContainer fromTrashcan={fromTrashcan} id={id} />
          ) : null,
        };
      case "test-execution":
        return {
          label: (
            <p>
              {t("Test execution")}{" "}
              <span className="counter">{countersData?.DQM || 0}</span>
              {/* <span className="mockup">{t("(mockup)")}</span>{" "} */}
              {/* <span className="counter">{10}</span> */}
            </p>
          ),
          key: "test-execution",
          children: isOpen ? (
            <TestExecutionContainer
              displaySaveCancel={displaySaveCancel}
              fromTrashcan={fromTrashcan}
            />
          ) : null,
        };

      case "test-chart":
        return {
          label: (
            <p>
              {t("Test performance chart")}{" "}
              <span className="counter">{countersData?.DQM || 0}</span>
              {/* <span className="mockup">{t("(mockup)")}</span>{" "} */}
              {/* <span className="counter">{10}</span> */}
            </p>
          ),
          key: "test-chart",
          children: isOpen ? <TestPerformanceChart /> : null,
        };
      case "comment":
        return {
          label: (
            <Dropdown
              trigger={["contextMenu"]}
              menu={{
                items: [
                  {
                    key: "add-comments",
                    label: "Add comment",
                    icon: <PlusOutlined />,
                  },
                ],
              }}
            >
              <p>
                {t("Comments")}{" "}
                <span className="counter">{countersData?.COMMENTS || 0}</span>
              </p>
            </Dropdown>
          ),
          key: "comment",
          children: isOpen ? (
            <CommentTableContainer
              displaySaveCancel={displaySaveCancel}
              fromTrashcan={fromTrashcan}
              id={id}
            />
          ) : null,
        };
    }
  };

  const getBottomNavigationMenus = (
    noGraph,
    id,
    countersData,
    showWorkingVersion: boolean,
    showAssets: boolean,
    fromTrashcan: boolean,
    displaySaveCancel: boolean,
    isOpen: boolean,
    showSuspects,
    isDQM,
    showLogs,
    showPermissions,
    fromMenuCreator
  ) => {
    const items = [];

    if (fromMenuCreator) {
      if (showPermissions) {
        ["permissions"].forEach((item: string) => {
          items.push(
            getItem(
              item,
              countersData,
              id,
              noGraph,
              fromTrashcan,
              displaySaveCancel,
              isOpen
            )
          );
        });
        return [...items];
      }
      return [];
    }
    let allNavItems = [...navigationItems];

    if (isDQM && !allNavItems.includes("test-chart")) {
      allNavItems = ["test-chart", ...allNavItems];
    }

    if (!isDQM && allNavItems.includes("test-chart")) {
      allNavItems = allNavItems?.filter(
        (item: string) => item !== "test-chart"
      );
    }

    if (isDQM && !allNavItems.includes("test-execution")) {
      allNavItems = ["test-execution", ...allNavItems];
    }

    if (!isDQM && allNavItems.includes("test-execution")) {
      allNavItems = allNavItems?.filter(
        (item: string) => item !== "test-execution"
      );
    }

    if (showWorkingVersion && !allNavItems.includes("working-version")) {
      allNavItems = ["working-version", ...allNavItems];
    }

    if (!showWorkingVersion && allNavItems.includes("working-version")) {
      allNavItems = allNavItems?.filter(
        (item: string) => item !== "working-version"
      );
    }

    if (showAssets && !allNavItems.includes("assets")) {
      allNavItems = ["assets", ...allNavItems];
    }

    if (!showAssets && allNavItems.includes("assets")) {
      allNavItems = allNavItems?.filter((item: string) => item !== "assets");
    }

    if (showPermissions && !allNavItems.includes("permissions")) {
      allNavItems = [...allNavItems, "permissions"];
    }

    if (!showPermissions && allNavItems.includes("permissions")) {
      allNavItems = allNavItems?.filter(
        (item: string) => item !== "permissions"
      );
    }

    if (showLogs && !allNavItems.includes("logs")) {
      allNavItems = [...allNavItems, "logs"];
    }

    if (!showLogs && allNavItems.includes("logs")) {
      allNavItems = allNavItems?.filter((item: string) => item !== "logs");
    }

    // REMOVE
    if (showSuspects && !allNavItems.includes("suspects")) {
      allNavItems.unshift("suspects");
    }
    if (!showSuspects && allNavItems.includes("suspects")) {
      allNavItems = allNavItems?.filter((item: string) => item !== "suspects");
    }

    allNavItems.forEach((item: string) => {
      items.push(
        getItem(
          item,
          countersData,
          id,
          noGraph,
          fromTrashcan,
          displaySaveCancel,
          isOpen
        )
      );
    });
    return [...items];
  };

  return { getBottomNavigationMenus };
};
