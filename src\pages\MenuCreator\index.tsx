import { styled } from "@linaria/react";
import { But<PERSON>, Empty } from "antd";
import { useEffect, useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { B_MENU_CREATOR, deepClone, useTheme } from "../../utils";
import { getHeaderNodes, updateHeaders } from "../../services";
import {
  AddNodeModal,
  BottomNavigationDrawer,
  BreadCrumb,
  DeleteNodeModal,
  Header,
  MenuCreatorTree,
  ResizableDiv,
} from "../../components";
import {
  generateRecursiveResponse,
  handleAssetAdd,
  handleMenuDelete,
  handleMenuRename,
  handleMultipleMenuDelete,
  handleParentMenuAdd,
} from "../../utils/functions/menuCreator";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import {
  useMenuCreatorGenerator,
  useNotification,
} from "../../utils/functions/customHooks";
import i18next from "i18next";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import {
  GET_ALL_TEMPLATES_KEY,
  GET_HEADER_MENUS,
  MENU_CREATOR_HOME_ID,
} from "../../constants";
import { ITemplates } from "../../interfaces";

export type IMenuCreatorKey =
  | "rename"
  | "add-asset"
  | "add-menu"
  | "delete"
  | "change-template"
  | "update-asset"
  | "set-permissions"
  | null;

interface IAction {
  id?: string;
  key: IMenuCreatorKey;
  label?: string;
  parentId?: string;
  type?: "menu" | "asset";
  templateIds?: any[];
  // allowedChildrens?: any[];
}

const STATE_KEY = "menucreator-sidebar";

const MenuCreatorPage = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const { contextHolder, showErrorNotification, showSuccessNotification } =
    useNotification();

  // const [showSetPermissions, setShowSetPermissions] = useState(false);

  const [treeData, setTreeData] = useState([]);
  const [tempTreeData, setTempTreeData] = useState([]);
  const [defaultExpanded, setDefaultExpanded] = useState([]);
  const [sidebarWidth, setSidebarWidth] = useState(260);
  const [selected, setSelected] = useState({ keys: [], info: [] });
  const [action, setAction] = useState<IAction>({
    key: null,
  });
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const bottomDrawerMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  const templatesData = queryClient.getQueryData(
    GET_ALL_TEMPLATES_KEY
  ) as ITemplates[];
  // const [hasNonAssetParent, setHasNonAssetParent] = useState(false);

  const { authenticated, globalPermissions } = useSelector(
    (root: RootState) => root.auth
  );
  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const { generateTreeDatas } = useMenuCreatorGenerator();
  // initial fetch
  const { isLoading, data } = useQuery(GET_HEADER_MENUS, getHeaderNodes, {
    enabled: !!authenticated,
  });

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs(B_MENU_CREATOR));
  }, []);

  useEffect(() => {
    const width = localStorage.getItem(STATE_KEY);
    if (width) setSidebarWidth(Number(width));
  }, []);

  useEffect(() => {
    if (data) {
      const allData = generateTreeDatas(
        [{ ...data.home, children: data.menu }],
        globalPermissions,
        handleActionsSelect
      );

      setTempTreeData(deepClone(allData));

      setTreeData(allData);
      if (allData?.length > 0) {
        setDefaultExpanded([MENU_CREATOR_HOME_ID]);
        setSelected({
          keys: [MENU_CREATOR_HOME_ID],
          info: [
            {
              id: MENU_CREATOR_HOME_ID,
              name: allData[0]?.name,
              parentId: 0,
              type: "menu",
              permissionsId: allData[0]?.permissionsId,
            },
          ],
        });
      }
    }
  }, [data, i18next.language]);

  // on dropdown items click
  const handleActionsSelect = (
    key: IMenuCreatorKey,
    id: string,
    label: string,
    parentId: string,
    templateIds?: any[]
  ) => {
    setDropdownOpen(true);
    // if (key === "set-permissions") {
    //   setShowSetPermissions(true);
    //   return;
    // }
    setAction({
      id: id,
      key: key,
      label: label,
      parentId: parentId,
      type: key === "add-menu" ? "menu" : "asset",
      templateIds: templateIds,
    });
  };

  // const checkTree = (nodes) => {
  //   let hasNoAssetParent = false;
  //   nodes.forEach((node) => {
  //     if (
  //       (!node.children || node.children.length === 0) &&
  //       node.type === "menu"
  //     ) {
  //       hasNoAssetParent = true;
  //       // setHasNonAssetParent(true);
  //       return true;
  //     }
  //     if (!hasNoAssetParent && node.children && node.children.length > 0) {
  //       checkTree(node.children);
  //     }
  //   });
  // };

  // useEffect(() => {
  //   // setHasNonAssetParent(false);
  //   checkTree(treeData);
  // }, [treeData]);

  const mutation = useMutation(updateHeaders, {
    onSuccess: () => {
      // refetch header query
      queryClient.invalidateQueries(GET_HEADER_MENUS);
      showSuccessNotification("Menu updated successfully!");
      setTempTreeData(deepClone([...treeData]));
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Error in saving menus!");
    },
  });

  // on save click
  const handleSave = () => {
    const newArray = [];
    treeData?.forEach((item) => newArray.push({ ...item }));
    const requestArray = generateRecursiveResponse(newArray, templatesData);
    mutation.mutate(requestArray);
  };

  const handleCancel = () => {
    setTreeData(deepClone(tempTreeData));
    dispatch(setMask(false));
  };

  return (
    <Wrapper style={{ border: mask ? "1px solid red" : "" }}>
      {bottomDrawerMask && <Mask className="mask" />}
      <BreadCrumb
        extra={
          mask && (
            <Buttons>
              {/* <Button type="primary" onClick={handleSave} disabled>
              {t("Save as working version")}
            </Button> */}
              {/* {hasNonAssetParent ? (
              <Tooltip
                title={t("Unable to save an empty menu")}
                placement="bottomLeft"
              >
                <Button type="primary" disabled={hasNonAssetParent}>
                  {t("Save")}
                </Button>
              </Tooltip>
            ) : ( */}
              <Button
                type="primary"
                onClick={handleCancel}
                className="cancel-button"
              >
                {t("Cancel")}
              </Button>
              <Button
                type="primary"
                onClick={handleSave}
                loading={mutation.isLoading}
              >
                {t("Save")}
              </Button>
              {/* )} */}
            </Buttons>
          )
        }
      />

      <Container
        theme={theme}
        className={bottomDrawerMask ? "bottom-mask-active" : ""}
      >
        {contextHolder}
        <Content>
          {isLoading ? (
            <div className="loader">
              <LoadingOutlined />
            </div>
          ) : (
            <>
              <ResizableDiv
                className={"resizable"}
                resize="right"
                onResize={(_event, _direction, ref) => {
                  if (ref.offsetWidth > 80) {
                    setSidebarWidth(ref.offsetWidth);
                  }
                }}
                maxWidth={"100%"}
                minWidth={80}
                width={sidebarWidth}
                defaultWidth="260px"
                saveWidthToLocalStorage
                stateKey={STATE_KEY}
              >
                <div className="tree-wrapper">
                  {treeData.length === 0 && !isLoading ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={t("No menus created yet!")}
                    />
                  ) : (
                    <MenuCreatorTree
                      treeData={treeData}
                      defaultExpanded={defaultExpanded}
                      setSelected={setSelected}
                      setTreeData={setTreeData}
                      handleActionsSelect={handleActionsSelect}
                      setDefaultExpanded={setDefaultExpanded}
                      selectedKeys={selected}
                      dropdownOpen={dropdownOpen}
                      setDropdownOpen={setDropdownOpen}
                    />
                  )}
                </div>
              </ResizableDiv>

              <RightDiv>
                <div className="header-wrapper">
                  <Header editMode previewData={treeData[0]?.children || []} />
                </div>
                {globalPermissions?.includes("PERMISSION") &&
                  selected?.info.length > 0 &&
                  selected?.info[0].type === "menu" &&
                  !selected?.info[0]?.isNew &&
                  selected?.info[0]?.id != MENU_CREATOR_HOME_ID && (
                    <BottomNavigationDrawer
                      fromMenuCreator
                      id={selected.keys[0]}
                      displaySaveCancel={bottomDrawerMask}
                    />
                  )}
              </RightDiv>
              {/* preview menu */}

              {/* action modal for add */}
              {(action.key?.startsWith("add") ||
                action.key === "rename" ||
                action.key === "update-asset") && (
                <AddNodeModal
                  fromMenuCreator
                  nodeType={4}
                  templateIds={action?.templateIds}
                  isAsset={
                    action?.key === "add-asset" ||
                    action?.key === "change-template" ||
                    action?.key === "update-asset"
                  }
                  id={Number(action.id)}
                  title={
                    action?.key === "update-asset"
                      ? t("Define allowed children types")
                      : action?.key === "rename"
                      ? t("Rename")
                      : action?.key === "add-asset"
                      ? t("Define allowed children types")
                      : t("Add menu")
                  }
                  label={action.label}
                  edit={
                    action.key === "rename" || action.key === "update-asset"
                  }
                  handleSave={
                    action.key === "add-asset" || action.key === "update-asset"
                      ? (allowedChildrens) => {
                          handleAssetAdd(
                            action.id,
                            globalPermissions,
                            allowedChildrens,
                            treeData,
                            setTreeData,
                            handleActionsSelect,
                            defaultExpanded,
                            setDefaultExpanded
                          );
                          dispatch(setMask(true));
                        }
                      : action.key?.startsWith("add")
                      ? (name: string, template: string) => {
                          const isUnique = handleParentMenuAdd(
                            name,
                            template,
                            treeData,
                            setTreeData,
                            action,
                            defaultExpanded,
                            setDefaultExpanded,
                            handleActionsSelect,
                            setSelected
                          );
                          if (isUnique) {
                            dispatch(setMask(true));
                          }
                          return isUnique;
                        }
                      : (newName: string, newTemplateId: number) => {
                          const isUnique = handleMenuRename(
                            newName,
                            treeData,
                            setTreeData,
                            action,
                            handleActionsSelect,
                            newTemplateId
                          );
                          if (isUnique) {
                            dispatch(setMask(true));
                          }
                          return isUnique;
                        }
                  }
                  isOpen={
                    action.key?.startsWith("add") ||
                    action.key === "rename" ||
                    action.key === "update-asset"
                  }
                  onClose={() => {
                    setDropdownOpen(false);
                    setAction({ ...action, key: null, parentId: null });
                  }}
                />
              )}

              {/* action modal for delete */}
              {action?.key?.startsWith("delete") && (
                <DeleteNodeModal
                  fromMenuCreator
                  onDeleteClick={() => {
                    selected.keys.length > 1
                      ? handleMultipleMenuDelete(
                          treeData,
                          setTreeData,
                          selected,
                          handleActionsSelect
                        )
                      : handleMenuDelete(
                          treeData,
                          setTreeData,
                          action,
                          handleActionsSelect
                        );
                    dispatch(setMask(true));
                  }}
                  isOpen={action?.key?.startsWith("delete")}
                  id={action.id}
                  label={action.label}
                  isMultiple={selected.keys.length > 1}
                  onClose={() => {
                    setDropdownOpen(false);
                    setAction({ key: null });
                  }}
                />
              )}

              {/* {!!showSetPermissions && (
                <SetPermissions
                  visible={showSetPermissions}
                  id={selected.keys[0]}
                  onClose={() => {
                    setShowSetPermissions(false);
                    setDropdownOpen(false);
                    setAction({ ...action, key: null, parentId: null });
                  }}
                />
              )} */}
            </>
          )}
        </Content>
      </Container>
    </Wrapper>
  );
};

export default MenuCreatorPage;

const Mask = styled.div`
  height: 100%;
  width: 100%;
`;

const RightDiv = styled.div`
  overflow: auto;
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const Buttons = styled.div`
  display: flex;
  gap: 9px;

  & button {
    font-size: 13px;
    height: 24px;
    padding: 0px 15px;
    border-radius: 3px;
  }
  & button:disabled {
    color: rgb(74 74 74);
    background-color: rgb(227 227 227);
  }
`;

const Content = styled.div`
  display: flex;
  height: 100%;

  & .resizable {
    display: flex;
    flex-direction: column;
  }
  & .ant-select {
    margin-bottom: 10px;
    margin-right: 10px;
    width: calc(100% - 20px);
  }

  & .ant-tree-iconEle {
    display: none !important;
  }

  & .ant-tree-node-selected {
    color: #094f8a !important;
  }
  & .ant-tree-switcher {
    background: transparent !important;
  }

  & .add-menu {
    border: 1px dashed #c0bebe;
    margin-right: 20px;
    padding: 4px 14px;
    font-size: 13px;
    border-radius: 3px;
    color: #545353;
    cursor: pointer;
  }
`;

const Container = styled.div<{ theme: any }>`
  background: #fff;
  flex: 1;
  padding: 10px 10px 2px 10px;
  min-height: 300px;
  height: calc(100% - 62px);
  overflow-y: auto;

  & .ant-divider {
    margin: 16px 0px;
  }
  & h4 {
    color: ${({ theme }) => theme?.colorPrimary};
    font-size: 14px;
    font-weight: 500;
    width: fit-content;
  }

  & .ant-tree {
    margin: 5px 8px 4px 0px;
    flex: 1;
    overflow: auto;
  }
  & .ant-empty-description {
    font-size: 12px;
  }
  & .loader {
    display: flex;
    width: 100%;
    justify-content: center;
    font-size: 32px;
  }
  & .tree-wrapper {
    border-right: 1px solid #eee;
    overflow-x: auto;
    min-height: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;

    & .search-wrapper {
      margin-top: auto;
      margin-right: 14px;
    }
    & .ant-tree-draggable-icon {
      min-width: 24px;
    }

    & .ant-dropdown-trigger {
      display: flex;
      align-items: center;
      gap: 7px;

      & svg {
        min-width: 16px;
        min-height: 16px;
      }
    }
    & .ant-tree-title {
      width: 100%;
    }
    & a {
      margin-left: 34px;
      color: #8a8a8a;
      border: 1px dashed;
      margin-right: 22px;
      padding: 5px 8px;
      border-radius: 3px;
      display: flex;
      gap: 7px;
      font-size: 13px;
    }
  }
  & .header-wrapper {
    flex: 1;
    margin-left: 30px;
    overflow: auto;

    & .ant-dropdown-trigger .folder-icon {
      display: none;
    }
  }
`;

const Wrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;

  & .bottom-mask-active .drawer {
    z-index: 7000;
    background: #fff;
  }

  & .new-menu {
    height: 16px;
    width: 16px;
    background: #fdf102;
    border-radius: 50%;
  }
`;
