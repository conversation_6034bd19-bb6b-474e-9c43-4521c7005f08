import { styled } from "@linaria/react";
import { <PERSON><PERSON>, Modal, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { getNodeDetails, moveToSelectedNode } from "../../../../services/node";
import { useEffect, useRef } from "react";
import {
  GET_CHILDRENS,
  GET_LOCAL_SETTINGS_KEY,
  GET_NODE_ATTRIBUTES_DETAILS,
  GET_NODE_DETAILS,
  getAttributeIcon,
  TEMP_GROUPING_NODE_ID,
  TRASHCAN_PARENT_NODE_ID,
} from "../../../../constants";
import { captureException } from "@sentry/react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { ILocalSettings, ITemplates } from "../../../../interfaces";
import { setPerformTrash } from "../../../../store/features/trashcan";
import { useNotification } from "../../../../utils/functions/customHooks";
import { useParams, useSearchParams } from "react-router-dom";
import { transformObjectPath } from "../../../../utils";
import { saveLocalSettings } from "../../../../services";
import { setPinned } from "../../../../store/features/pinned";

const MoveTrashToSelected = ({
  onClose,
  isOpen,
  isMultiple,
  action,
}: Props) => {
  const { t } = useTranslation();
  const ref = useRef<any>();
  const params = useParams();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { contextHolder, showErrorNotification } = useNotification();
  const [searchParams] = useSearchParams();

  const { label, templateId, isLeaf } = action;

  const pinned = useSelector((state: RootState) => state.pinned.pinned);
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );
  const selectedTrash = useSelector(
    (state: RootState) => state.trash.selectedTrash
  );
  const selected = useSelector((state: RootState) => state.sidebar.selected);

  const toMoveNodeId =
    selected.keys.length > 0 ? selected.keys[0] : params?.nodeId;

  useEffect(() => {
    ref?.current?.focus();
  }, [isOpen]);

  const settingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const pinnedMutation = useMutation(saveLocalSettings);

  const updatePinned = (newPinned) => {
    pinnedMutation.mutateAsync({
      value: {
        ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
        pinned: newPinned,
      },
    });
    dispatch(setPinned([...newPinned]));
  };

  const moveToSelectedMutation = useMutation(moveToSelectedNode, {
    onSuccess: () => {
      const newPinned = [];

      pinned?.forEach((pin) => {
        if (selectedTrash.keys.includes(pin.id)) {
          newPinned.push({ ...pin, inTrash: false, parentId: toMoveNodeId });
        } else {
          newPinned.push({ ...pin });
        }
      });

      if (newPinned.length > 0) {
        updatePinned(newPinned);
      }

      dispatch(
        setPerformTrash({
          treeNode: toMoveNodeId,
        })
      );

      notification.success({
        message: t("Success!"),
        description: t("Node restored to selected successfully!"),
      });

      // moved to root
      if (selected.keys.length === 0) {
        queryClient.invalidateQueries([GET_CHILDRENS, toMoveNodeId.toString()]);
      }

      if (searchParams.get("nodeId")) {
        queryClient.invalidateQueries([
          GET_NODE_ATTRIBUTES_DETAILS,
          searchParams.get("nodeId"),
        ]);
      }
      queryClient.invalidateQueries([GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID]);
      onClose();
    },
    onError: (e: any) => {
      notification.error({
        message: "Error Occurred!",
        description: e?.data?.error || "Please try again after sometime",
      });
      captureException(e?.data?.error);
    },
  });

  const handleMoveToSelected = () => {
    if (selected.keys.length === 0) {
      const allowedChildrensOfMenu = nodeDetails?.data?.body[0]?.value?.map(
        (allowed) => allowed.id
      ) as number[];

      if (allowedChildrensOfMenu?.length === 0) {
        showErrorNotification(
          t("DRAG_RESTRICTED_NO_ALLOWED_CHILDREN", {
            name: nodeDetails?.data?.name,
          })
        );
        return;
      }

      const notAllowedTemplates = [];
      selectedTrash?.info?.forEach((selected) => {
        if (!allowedChildrensOfMenu.includes(selected.templateId)) {
          const templateName = templatesData[selected.templateId]?.name || "-";
          notAllowedTemplates.push(templateName);
        }
      });

      if (notAllowedTemplates.length > 0) {
        showErrorNotification(
          t("DRAG_RESTRICTED_TYPE_MISMATCHED", {
            parent: nodeDetails?.data?.name,
            child: notAllowedTemplates.join(", "),
          }),
          "Move operation restricted!"
        );
        return;
      }
    } else {
      const parentTemplate = templatesData[
        nodeDetails?.data?.templateId
      ] as ITemplates;

      let allowedChildren = null;

      if (nodeDetails?.data?.templateId === TEMP_GROUPING_NODE_ID) {
        allowedChildren = nodeDetails?.data?.body?.find(
          (attr) => attr.type === "allowedChildren"
        )?.value;
      } else {
        allowedChildren = parentTemplate?.allowedChildren;
      }
      if (allowedChildren === 0) {
        showErrorNotification(
          t("DRAG_RESTRICTED_NO_ALLOWED_CHILDREN", {
            name: parentTemplate?.name,
          })
        );
        return;
      }

      const parentAllowedChildrens = allowedChildren?.map(
        (allowed) => allowed.id
      );
      const notAllowedTemplates = [];
      selectedTrash?.info?.forEach((selected) => {
        if (!parentAllowedChildrens?.includes(selected.templateId)) {
          const templateName = templatesData[selected.templateId]?.name || "-";

          notAllowedTemplates.push(templateName);
        }
      });

      if (notAllowedTemplates.length > 0) {
        showErrorNotification(
          t("DRAG_RESTRICTED_TYPE_MISMATCHED", {
            parent: parentTemplate?.name,
            child: notAllowedTemplates.join(", "),
          }),
          "Move operation restricted!"
        );
        return;
      }
    }

    moveToSelectedMutation.mutate({
      parentId: toMoveNodeId,
      nodeIds: selectedTrash.keys,
    });
  };

  let templateIcon = null;
  if (!isMultiple) {
    templateIcon = templatesData[Number(templateId)]?.icon || "_30_folder";
  }

  const nodeDetails = useQuery(
    [GET_NODE_DETAILS, toMoveNodeId.toString()],
    () => getNodeDetails(toMoveNodeId)
  ) as any;

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      centered
      className="export-modal"
      title={
        isMultiple
          ? `${t("Restore selected")}?`
          : `${t("Restore to")} ${nodeDetails?.data?.name}?`
      }
      width="50%"
    >
      {contextHolder}
      <Wrapper>
        {!isMultiple && selected.keys.length > 0 && (
          <p>
            {getAttributeIcon(templateIcon)} {label}
          </p>
        )}

        <h5>
          {isMultiple ? (
            t("Are you sure you want to restore these selected items?")
          ) : (
            <>
              {t("Are you sure you want to restore the item")}
              {!isLeaf && (
                <span>
                  {t("Be minded that the entire branch will be moved")}
                </span>
              )}
            </>
          )}
        </h5>

        <p className="path">
          {t("new_path_info", {
            label: selectedTrash.keys.length > 1 ? t("selected nodes") : label,
          })}
          <span>
            {transformObjectPath(nodeDetails?.data?.pathName, false)}
            {selectedTrash.keys.length <= 1 && ` > ${label}`}
          </span>
        </p>

        <Button
          onClick={handleMoveToSelected}
          type="primary"
          ref={ref}
          loading={moveToSelectedMutation.isLoading}
        >
          {isMultiple ? t("Restore selected") : t("Restore")}
        </Button>
      </Wrapper>
    </Modal>
  );
};

export { MoveTrashToSelected };

interface Props {
  action: {
    id: string;
    label: string;
    templateId?: number;
    isLeaf: boolean;
  };
  onClose: () => void;
  isOpen: boolean;
  isMultiple?: boolean;
  trashData: any;
  setTrashData: any;
}

const Wrapper = styled.form`
  padding-bottom: 20px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;

  & .path {
    margin-top: 10px;
    color: grey;
    gap: 5px;
    font-size: 13px;
    flex-wrap: wrap;
    justify-content: center;

    & span {
      color: #4277a2;
    }
  }

  & img {
    object-fit: contain;
  }

  & h5 {
    font-size: 13px;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 6px;

    & span {
      color: #949494;
      font-size: 12px;
      display: block;
    }
  }

  & p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4277a2;

    & svg {
      width: 20px;
      height: 20px;
    }
  }
  & button {
    margin-left: auto;
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }
`;
