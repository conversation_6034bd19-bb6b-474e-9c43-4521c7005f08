import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { getNodeDetails } from "../../../services/node";
import { useQuery } from "react-query";
import {
  ATTACHMENT_NODE_ID,
  GET_NODE_ATTRIBUTES_DETAILS,
  PERMISSIONS_NODE_ID,
} from "../../../constants";
import { IAttributes } from "../../../interfaces";
import { ProgressSpinner } from "primereact/progressspinner";
import { useTranslation } from "react-i18next";
import { AttributeItem } from "../../atoms";
import { getAttributeTitleWidth } from "../../../utils";

const TITLE_CLASSNAME = "details-expand";

const DetailCellRenderer = (props) => {
  const id = props.data.id;
  const { t } = useTranslation();
  const [attributes, setAttributes] = useState([]);
  const [hasNewAttributes, setHasNewAttributes] = useState(false);

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const { data: nodeDetails, isLoading } = useQuery(
    [GET_NODE_ATTRIBUTES_DETAILS, id],
    () => getNodeDetails(id)
  );

  useEffect(() => {
    if (!nodeDetails) {
      return;
    }
    const attributes = [];
    const selectedTemplateAttributes =
      templatesData[Number(nodeDetails.templateId)]?.attributeTemplates || [];

    const nodeAttributes = nodeDetails?.body?.filter(
      (attr) =>
        attr.id !== PERMISSIONS_NODE_ID && attr.id !== ATTACHMENT_NODE_ID
    );

    setHasNewAttributes(
      selectedTemplateAttributes?.length > nodeAttributes?.length
    );

    selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
      const attributeValue = nodeDetails?.body?.find(
        (item) => item.id == attribute.id
      );
      if (attributeValue) {
        attributes.push({
          ...attributeValue,
          ...attribute,
          value:
            attribute.type === "multiplicity"
              ? {
                  text1: attributeValue?.value?.split("..")[0],
                  text2: attributeValue?.value?.split("..")[1],
                }
              : attribute.type === "switch"
              ? attributeValue?.value || false
              : attributeValue?.value,
        });
      }
    });
    setAttributes([...attributes]);

    setTimeout(() => {
      const titles = document.querySelectorAll(`.${TITLE_CLASSNAME}`) as any;

      titles.forEach((title) => {
        title.style.width = `fit-content`;
      });

      const maxTitleWidth = getAttributeTitleWidth(`.${TITLE_CLASSNAME}`);
      titles.forEach((title) => {
        title.style.width = `${maxTitleWidth}px`;
      });
    }, 300);
  }, [nodeDetails]);

  return (
    <div style={{ padding: "10px", background: "#f9f9f9" }}>
      {isLoading ? (
        <ProgressSpinner style={{ height: 30 }} />
      ) : attributes?.length === 0 ? (
        hasNewAttributes ? (
          <div className="new-attributes">
            <p>{t("New attributes exists")}</p>
          </div>
        ) : (
          <p>{t("No attributes")}</p>
        )
      ) : (
        <>
          {attributes?.map((attribute) => (
            <AttributeItem
              readOnly
              key={attribute.id}
              {...attribute}
              title={attribute.name}
              titleClassName={TITLE_CLASSNAME}
            />
          ))}

          {hasNewAttributes && (
            <div className="new-attributes">
              <p>{t("New attributes exists")}</p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export { DetailCellRenderer };
