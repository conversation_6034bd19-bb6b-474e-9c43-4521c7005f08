import { css } from "@linaria/core";
import { Resizable } from "re-resizable";
import { ReactNode } from "react";
import { EllipsisOutlined, MoreOutlined } from "@ant-design/icons";
import { styled } from "@linaria/react";

interface Props {
  children: ReactNode;
  onResize?: any;
  defaultWidth?: string;
  defaultHeight?: string;
  maxWidth?: string;
  minWidth?: number;
  maxHeight?: string;
  minHeight?: number;
  resize: "top" | "right" | "left";
  height?: any;
  width?: any;
  className?: any;
  saveWidthToLocalStorage?: boolean;
  saveHeightToLocalStorage?: boolean;
  stateKey?: string;
  style?: any;
}

const ResizableDiv = ({
  children,
  onResize,
  defaultHeight,
  defaultWidth,
  maxWidth,
  minWidth,
  maxHeight,
  resize,
  className,
  minHeight,
  saveWidthToLocalStorage,
  saveHeightToLocalStorage,
  stateKey,
  height,
  width,
  style,
}: Props) => {
  const getResizeDirection = () => {
    switch (resize) {
      case "top": {
        return {
          top: true,
          right: false,
          bottom: false,
          left: false,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        };
      }
      case "left": {
        return {
          top: false,
          right: false,
          bottom: false,
          left: true,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        };
      }

      case "right": {
        return {
          top: false,
          right: true,
          bottom: false,
          left: false,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        };
      }
    }
  };

  if (resize === "top") {
    return (
      <Resizable
        style={style}
        onResize={onResize && onResize}
        onResizeStop={(_event, _direction, ref) => {
          if (
            saveHeightToLocalStorage &&
            stateKey &&
            ref.offsetHeight > minHeight
          ) {
            localStorage.setItem(stateKey, ref.offsetHeight.toString());
          }
        }}
        defaultSize={{
          width: defaultWidth || "100%",
          height: defaultHeight || "100%",
        }}
        maxWidth={maxWidth}
        minWidth={minWidth}
        maxHeight={maxHeight}
        size={{ height: height, width: width }}
        minHeight={minHeight}
        enable={getResizeDirection()}
        className={`${ContainerCSS} ${className}`}
      >
        <TopIndicator className="indicator">
          <EllipsisOutlined />
        </TopIndicator>

        {children}
      </Resizable>
    );
  }
  return (
    <Resizable
      onResize={onResize && onResize}
      onResizeStop={(_event, _direction, ref) => {
        if (saveWidthToLocalStorage && stateKey && ref.offsetWidth > minWidth) {
          localStorage.setItem(stateKey, ref.offsetWidth.toString());
        }
      }}
      size={width || height ? { height: height, width: width } : undefined}
      defaultSize={{
        width: defaultWidth || "100%",
        height: defaultHeight || "100%",
      }}
      maxWidth={maxWidth}
      minWidth={minWidth}
      enable={getResizeDirection()}
      className={`${ContainerCSS} ${className}`}
      style={style}
    >
      <Indicator
        className={`indicator ${resize === "left" ? "left-indicator" : ""}`}
      >
        <MoreOutlined />
      </Indicator>

      {children}
    </Resizable>
  );
};

export { ResizableDiv };

const TopIndicator = styled.div`
  font-size: 30px;
  color: #e0dbdb;
  position: absolute;
  top: -26px;
  z-index: 1;
  pointer-events: none;
  left: 50%;
  transform: translate(-50%, 0px);
`;

const Indicator = styled.div`
  font-size: 30px;
  color: #e0dbdb;
  position: absolute;
  right: -10px;
  z-index: 1;
  top: 45%;
  pointer-events: none;
`;

const ContainerCSS = css`
  & .left-indicator {
    right: unset;
    left: -10px;
  }
`;
