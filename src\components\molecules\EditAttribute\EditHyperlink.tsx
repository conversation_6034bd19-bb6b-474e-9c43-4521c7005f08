import { useMutation, useQuery, useQueryClient } from "react-query";
import { getHyperLinks } from "../../../services/attribute";
import { DetailsContainer, IAGColumns, MyTable } from "../../organisms";
import { useEffect, useMemo, useState, useTransition } from "react";
import { Button, Dropdown } from "antd";
import {
  getAttributeTitleWidth,
  getParentID,
  transformObjectPath,
} from "../../../utils";
import {
  GET_HYPERLINKS,
  GET_LOCAL_SETTINGS_KEY,
  GET_NODE_ATTRIBUTES_DETAILS,
  getAttributeIcon,
} from "../../../constants";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { useSearchParams } from "react-router-dom";
import {
  setAttributeMask,
  setDisplaySaveButton,
  setMask,
} from "../../../store/features";
import {
  useHyperlinkActions,
  useNotification,
} from "../../../utils/functions/customHooks";
import { saveLocalSettings } from "../../../services";
import { styled } from "@linaria/react";
import { ILocalSettings } from "../../../interfaces";

const TITLE_CLASSNAME = "hyperlinktables-title";
const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

const EditHyperlink = ({ val, setVal, onEdit, id, dropdownItems }) => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();

  const { handleHyperlinkAction } = useHyperlinkActions();
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [columnsRequest, setColumnsRequest] = useState([]);
  const [columns, setColumns] = useState([]);
  const [displaySaveCancel, setDisplaySaveCancel] = useState(false);
  const { mask, attributeMask } = useSelector(
    (root: RootState) => root.sidebar
  );
  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isPending, startTransition] = useTransition();

  const nodeDetails = queryClient.getQueryData([
    GET_NODE_ATTRIBUTES_DETAILS,
    searchParams.get("nodeId"),
  ]) as any;

  const [multiplicity, setMultiplicity] = useState("");
  const [data, setData] = useState([]);
  const [initialLoad, setInitialLoad] = useState(true);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [contextMenuOpen, setContextMenuOpen] = useState(null);
  const [selected, setSelected] = useState(null);
  const [resetTrigger, setResetTrigger] = useState(0);

  const hyperlinkOptionsIds = dropdownItems?.map((item) =>
    item?.id?.toString()
  );

  const { isLoading, isFetching, isError } = useQuery<any>(
    [GET_HYPERLINKS, hyperlinkOptionsIds.toString()],
    () => getHyperLinks(hyperlinkOptionsIds),
    {
      onSuccess: (data) => {
        setData(
          data?.filter(
            (item) =>
              !item.inTrash ||
              (item.inTrash &&
                val?.some((hyperlink) => hyperlink?.id === item.id))
          )
        );
      },
      enabled: hyperlinkOptionsIds.length > 0,
    }
  );

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }

    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.relationsTable &&
      localSettingsData?.body[0]?.value?.relationsTable?.columns.length > 0
    ) {
      if (localSettingsData?.body[0]?.value?.relationsTable?.columns) {
        const pinned =
          localSettingsData?.body[0]?.value?.relationsTable?.pinned || [];
        const sort =
          localSettingsData?.body[0]?.value?.relationsTable?.sort || [];

        const allColumns = [];
        localSettingsData.body[0].value.relationsTable.columns?.forEach(
          (column) => {
            const index = COLUMNS.findIndex((item) => item.field === column);
            allColumns.push({
              ...COLUMNS[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
          }
        );
        setColumnsRequest(
          localSettingsData?.body[0]?.value?.relationsTable?.columns
        );
        setPinned(localSettingsData?.body[0]?.value?.relationsTable?.pinned);
        setSort(localSettingsData?.body[0]?.value?.relationsTable?.sort);
        setFilters(localSettingsData?.body[0]?.value?.relationsTable?.filters);
        setColumns(allColumns);
      }
    } else {
      setColumns(COLUMNS);
      setColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger]);

  useEffect(() => {
    if (initialLoad && val) {
      setSelected(val);
      setInitialLoad(false);
    }
  }, [initialLoad, val]);

  const HYPERLINKS_ACTIONS = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("Open in new tab"),
      key: "open-in-new-tab",
    },
  ];

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        setContextMenuOpen(false);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
      }
    }
  };

  const COLUMNS = useMemo(() => {
    return [
      {
        headerName: t("Asset Name"),
        flex: 1,
        minWidth: 160,
        cellRenderer: "agGroupCellRenderer",
        cellRendererParams: {
          innerRenderer: (event) => {
            const record = event?.data;
            return (
              <Dropdown
                menu={{
                  items: HYPERLINKS_ACTIONS,
                  onClick: (e) =>
                    handleNodeClick(e.key, record.id, record.name),
                }}
                trigger={["contextMenu"]}
                onOpenChange={(open) => setContextMenuOpen(open)}
              >
                <p
                  className={`title-container ${
                    record.inTrash ? "trash-hyperlink" : ""
                  }`}
                  onClick={async (e) => {
                    e.stopPropagation();

                    handleHyperlinkAction({
                      id: record.id,
                      inTrash: record.inTrash,
                    });
                  }}
                  style={{ wordBreak: "break-all" }}
                >
                  {record?.name || "-"}
                </p>
              </Dropdown>
            );
          },
        },
        field: "name",
      },
      {
        headerName: "Object Template",
        field: "templateId",
        minWidth: 150,
        flex: 1,
        cellRenderer: (event) => {
          const record = event?.data;
          const selectedTemplate = templatesData[Number(record?.templateId)];
          const templateIcon = selectedTemplate?.icon || "_30_folder";

          if (selectedTemplate) {
            return (
              <p className="title-container">
                {getAttributeIcon(templateIcon)}
                {selectedTemplate.name}
              </p>
            );
          }
          return "-";
        },
      },
      {
        headerName: "Path",
        field: "path",
        minWidth: 200,
        flex: 1,
        cellRenderer: ({ data }) => (
          <p className="right-align">
            {data?.path ? transformObjectPath(data?.path, data.inTrash) : "-"}
          </p>
        ),
      },
    ] as IAGColumns[];
  }, [mask]);

  useEffect(() => {
    const titles = document.querySelectorAll(`.${TITLE_CLASSNAME}`) as any;

    titles.forEach((title) => {
      title.style.width = `fit-content`;
    });

    const maxTitleWidth = getAttributeTitleWidth(`.${TITLE_CLASSNAME}`);
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });
  }, [data]);

  useEffect(() => {
    if (searchParams?.get("template")) {
      const selectedTemplate =
        templatesData[Number(searchParams?.get("template"))];
      const selectedAttribute = selectedTemplate?.attributeTemplates?.find(
        (attr) => attr.id === id
      );

      setMultiplicity(selectedAttribute?.multiplicity);
    } else {
      const selectedTemplate = templatesData[nodeDetails?.templateId];
      const selectedAttribute = selectedTemplate?.attributeTemplates?.find(
        (attr) => attr.id === id
      );
      setMultiplicity(selectedAttribute?.multiplicity);
    }
  }, [id, templatesData, searchParams.get("draft")]);

  const handleCancel = () => {
    setResetTrigger((trigger) => trigger + 1);
    setDisplaySaveCancel(false);
    setTimeout(() => {
      dispatch(setAttributeMask(false));
    }, 200);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setAttributeMask(false));
      setDisplaySaveCancel(false);
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setAttributeMask(false));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        relationsTable: {
          columns: columnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  return (
    <Wrapper
      onClick={(e) => e.stopPropagation()}
      style={{
        border: attributeMask ? "1px solid red" : "none",
        zIndex: attributeMask ? 100000 : "inherit",
        position: "relative",
        background: attributeMask ? "#fff" : "transparent",
        padding: attributeMask ? "10px" : "0px",
      }}
    >
      {contextHolder}
      {displaySaveCancel && (
        <div className="buttons-wrapper">
          <Button
            className="breadcrumb-button cancel-button"
            type="primary"
            onClick={handleCancel}
          >
            {t("Cancel")}
          </Button>
          <Button
            className="breadcrumb-button save-button"
            type="primary"
            onClick={handleSave}
            loading={mutation.isLoading}
          >
            {t("Save")}
          </Button>
        </div>
      )}
      <MyTable
        isError={isError}
        excelFileName="relations"
        multiplicity={multiplicity}
        loading={isLoading || isFetching}
        columns={columns}
        data={data}
        detectChange={() => {
          dispatch(setAttributeMask(true));
          setDisplaySaveCancel(true);
        }}
        defaultSelected={selected}
        onSelect={(selected) => {
          dispatch(setMask(true));
          dispatch(setDisplaySaveButton(true));
          setSelected(selected);

          if (selected?.length > 0) {
            const selectedKeys = [];
            // const templateId = breadcrumb[breadcrumb.length - 1]?.templateId;
            const selectedOptions = selected;

            selected?.forEach(async (item) => {
              // if (!hasAttributeLookup) {
              selectedKeys.push({
                id: item?.id,
                name: item?.name,
                templateId: item?.templateId,
                inTrash: item?.inTrash,
                pathName: item?.path,
                attributeLookup: null,
                templateHasAttributes: item?.templateHasAttributes,
              });
              // } else {
              //   let allAttributes = null;
              //   allAttributes = queryClient.getQueryData([
              //     GET_NODE_ATTRIBUTES_DETAILS,
              //     item?.id?.toString(),
              //   ]);
              //   console.log("att1", allAttributes);
              //   if (!allAttributes) {
              //     allAttributes = await getNodeDetails(item?.id);
              //     queryClient.setQueryData(
              //       [GET_NODE_ATTRIBUTES_DETAILS, item?.id?.toString()],
              //       allAttributes
              //     );
              //   }

              //   console.log("fetched", allAttributes);
              //   const selectedTemplate =
              //     templatesData[templateId]?.attributeTemplates || [];

              //   const selectedAttributeLookup = selectedTemplate?.find(
              //     (attr) => attr.id === id
              //   )?.attributeLookup;

              //   const lookupValue = [];
              //   if (selectedAttributeLookup) {
              //     const attrLookup = selectedAttributeLookup[item?.templateId];

              //     attrLookup?.forEach((attr) => {
              //       const selected = allAttributes?.body?.find(
              //         (att) => att.id === attr
              //       );
              //       lookupValue.push({ ...selected });
              //     });
              //   }
              //   selectedKeys.push({
              //     attributeLookup: lookupValue,
              //     id: item.id,
              //     name: item.name,
              //     inTrash: item.inTrash,
              //     templateHasAttributes: item.templateHasAttributes,
              //   });
              // }
            });
            startTransition(() => {
              if (!contextMenuOpen) {
                if (selectedOptions) {
                  setVal(selectedKeys);
                  onEdit(selectedKeys);
                } else {
                  setVal(selectedOptions);
                  onEdit(selectedOptions);
                }
              }
            });
          } else {
            startTransition(() => {
              if (!contextMenuOpen) {
                setVal([]);
                onEdit([]);
              }
            });
          }
        }}
        noHeader
        setFilters={setFilters}
        setSort={setSort}
        setPinned={setPinned}
        setColumnsRequest={setColumnsRequest}
        resetTrigger={resetTrigger}
        initialFilters={
          localSettingsData?.body[0]?.value?.relationsTable?.filters || {}
        }
      />

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Wrapper>
  );
};

export { EditHyperlink };

const Wrapper = styled.div`
  & .buttons-wrapper {
    display: flex;
    gap: 10px;
    justify-content: right;
    padding-bottom: 10px;
  }
`;

// const Multiplicity = styled.div`
//   border: 1px solid #d1d5db;
//   padding: 6px 10px;
//   min-width: 60px;
//   border-radius: 2rem;
//   text-align: center;
//   color: #4b5563;
// `;
