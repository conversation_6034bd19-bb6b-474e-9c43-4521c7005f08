import { useState, useEffect, useCallback } from "react";
import { getParentNameDetails } from "../../../services/node";
import { debounce } from "lodash";

/**
 * Target Location Attribute IDs
 *
 * These specific attribute IDs (-322, -389, -417, -337) are fixed in the backend system
 * and represent target location attributes used for connectors. When these attributes
 * have a valid positive numeric ID as their value, we need to fetch and display the
 * corresponding node name in a second column.
 * @link https://valuetank.atlassian.net/wiki/spaces/C3K/pages/234979329/Frontend+hard-coded+rules
 */
export const TARGET_LOCATION_ATTRIBUTE_IDS = [-322, -389, -417, -337];

/**
 * Custom hook to fetch target location details
 *
 * @param value The value of the attribute (should be a positive numeric ID)
 * @returns The name of the target location or null if not found, and loading state
 */
export const useTargetLocation = (value: number | null) => {
  const [targetLocationName, setTargetLocationName] = useState<string | null>(
    null
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Create a debounced fetch function using lodash
  const debouncedFetch = useCallback(
    debounce(async (nodeId: number) => {
      try {
        const response = await getParentNameDetails(nodeId);

        if (response && response.length > 0) {
          setTargetLocationName(response[0].name);
        } else {
          setTargetLocationName(null);
        }
      } finally {
        setIsLoading(false);
      }
    }, 1000),
    []
  );

  useEffect(() => {
    // Only fetch if value is a valid positive number
    if (value && typeof value === "number" && value > 0) {
      setIsLoading(true);
      debouncedFetch(value);
    } else {
      setTargetLocationName(null);
      setIsLoading(false);
    }

    // Cleanup function to cancel debounced calls when component unmounts or value changes
    return () => {
      debouncedFetch.cancel();
    };
  }, [value, debouncedFetch]);

  return { targetLocationName, isLoading };
};
