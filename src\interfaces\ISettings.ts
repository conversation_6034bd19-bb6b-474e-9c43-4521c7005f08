import { IPinned } from "./common";

export type IGeneralSettings = {
  application_name: string;
  logo: string;
  homeLayoutType: "kanban" | "list";
  sections: string[];
  hasShortcut: boolean;
  showSearchHistory: boolean;
  showLogo: boolean;
  showDescription: boolean;
  displayFooterText: boolean;
  footerTextAlignment: "center" | "left" | "right";
  footerText: string;
  hiddenAttributes: string;
};

type ITranslationBody = {
  value: {
    languages?: any;
    translations?: any;
  };
};

type IGlobalBody = {
  value: {
    globalFilters?: any;
    myFilters?: any;
    settings?: any;
    general: IGeneralSettings;
  };
};

export type IGlobalSettings = {
  id: number;
  body: IGlobalBody[];
};

export type ITranslationsSettings = {
  id: number;
  body: ITranslationBody[];
};

type ILocalBody = {
  value: {
    language: string;
    settings?: any;
    settingsStyle?: any;
    homeSidebarWidth?: number;
    homeLayout?: "v1" | "v2" | "v3";
    homeSections?: any;
    homev1Sections?: any;
    homeLayoutType: any;
    homeIconPosition?: any;
    colors: {
      colorPrimary: string;
      colorSecondary: string;
      background: string;
      bgLight: string;
      bgAttributes: string;
      breadcrumbsColor: string;
      trashBreadcrumbsFontColor: string;
      trashBreadcrumbsColor: string;
      metamodelBreadcrumbsFontColor: string;
      metamodelBreadcrumbsColor: string;
    };
    pinned?: IPinned[];
    generalSettingsTable?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    appPropertiesTable?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    messageTable?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    relationsTable?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    searchTable?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    relationAttribute?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    favoritesTable?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    myGradesTable?: {
      columns: any[];
      filters: any;
      sort: any;
    };
    myFilters: any;
    relationsDrawer?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    permissionsDrawer?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    assetsDrawer?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };

    commentsDrawer?: {
      columns: any[];
      filters: any;
      pinned: any[];
      sort: any;
    };
    historyDrawer?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    logsDrawer?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    testExecution?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    suspectsDrawer?: {
      columns: any[];
      filters: any;
      pinned: any[];
      sort: any;
    };
    attachmentDrawer?: {
      columns: any[];
      filters: any;
      pinned: any[];
      sort: any;
    };
    myCommentsTable?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    historyTable?: {
      columns: any[];
      filters: any;
      pinned: any[];
      sort: any;
    };
    technicalLogs?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };

    businessLogs?: {
      columns: any[];
      filters: any;
      sort: any;
      pinned: any[];
    };
    trashcanTable?: {
      columns: any[];
      filters: any;
      sort: any;
    };
    navigationItems: string[];
  };
};

export type ILocalSettings = {
  id: number;
  body: ILocalBody[];
};
