import React, { memo } from "react";

import type { CustomCellEditorProps } from "ag-grid-react";
import { Switch } from "antd";

export default memo(({ value, onValueChange }: CustomCellEditorProps) => {
  const onClick = (value) => {
    onValueChange(value ? "true" : "false");
  };

  return (
    <div tabIndex={1}>
      <Switch value={value === "true"} onChange={(value) => onClick(value)} />
    </div>
  );
});
