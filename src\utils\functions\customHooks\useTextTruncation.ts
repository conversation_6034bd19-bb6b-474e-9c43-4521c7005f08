import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { debounce } from "lodash";
import { extractTextFromHTML, truncateTextToWidth } from "../textUtils";

interface UseTextTruncationOptions {
  isHtml?: boolean;
  iconSpacing?: number;
  debounceMs?: number;
  resizeDebounceMs?: number;
}

interface UseTextTruncationReturn {
  isExpanded: boolean;
  isOverflowing: boolean;
  truncatedText: string;
  measureRef: React.RefObject<HTMLSpanElement>;
  containerRef: React.RefObject<HTMLDivElement>;
  handleToggle: (e: React.MouseEvent) => void;
  setIsExpanded: (expanded: boolean) => void;
}

/**
 * Custom hook for text truncation with expand/collapse functionality
 * Optimized for performance with proper memoization and debouncing
 */
export const useTextTruncation = (
  value: string,
  options: UseTextTruncationOptions = {}
): UseTextTruncationReturn => {
  const {
    isHtml = false,
    iconSpacing = 25,
    debounceMs = 100,
    resizeDebounceMs = 50,
  } = options;

  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [truncatedText, setTruncatedText] = useState('');
  
  const measureRef = useRef<HTMLSpanElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // Memoize the plain text extraction to avoid recalculation
  const plainText = useMemo(() => {
    if (!value) return '';
    return isHtml ? extractTextFromHTML(value) : value;
  }, [value, isHtml]);

  // Memoized measure and truncate function
  const measureAndTruncate = useCallback(() => {
    if (!measureRef.current || !containerRef.current || !plainText) {
      setIsOverflowing(false);
      setTruncatedText(plainText);
      return;
    }

    const availableWidth = containerRef.current.offsetWidth - iconSpacing;
    if (availableWidth <= 0) {
      setIsOverflowing(false);
      setTruncatedText(plainText);
      return;
    }

    const result = truncateTextToWidth(plainText, availableWidth, measureRef.current);
    const hasOverflow = result !== plainText;
    
    setIsOverflowing(hasOverflow);
    setTruncatedText(result);
  }, [plainText, iconSpacing]);

  // Debounced version for initial measurement
  const debouncedMeasureAndTruncate = useMemo(
    () => debounce(measureAndTruncate, debounceMs),
    [measureAndTruncate, debounceMs]
  );

  // Debounced version for resize events
  const debouncedResizeMeasure = useMemo(
    () => debounce(measureAndTruncate, resizeDebounceMs),
    [measureAndTruncate, resizeDebounceMs]
  );

  // Handle toggle with event propagation prevention
  const handleToggle = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(prev => !prev);
  }, []);

  // Setup ResizeObserver and initial measurement
  useEffect(() => {
    if (!containerRef.current) return;

    // Initial measurement with debounce
    const timeoutId = setTimeout(debouncedMeasureAndTruncate, debounceMs);

    // Setup ResizeObserver
    resizeObserverRef.current = new ResizeObserver(() => {
      debouncedResizeMeasure();
    });

    resizeObserverRef.current.observe(containerRef.current);

    return () => {
      clearTimeout(timeoutId);
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      debouncedMeasureAndTruncate.cancel();
      debouncedResizeMeasure.cancel();
    };
  }, [debouncedMeasureAndTruncate, debouncedResizeMeasure, debounceMs]);

  // Reset expansion state when value changes
  useEffect(() => {
    setIsExpanded(false);
  }, [value]);

  return {
    isExpanded,
    isOverflowing,
    truncatedText,
    measureRef,
    containerRef,
    handleToggle,
    setIsExpanded,
  };
};
